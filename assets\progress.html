<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Picasso - Create & Sell Art</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    
    <!-- Firebase App (the core Firebase SDK) -->
    <script type="module" src="firebase-config.js"></script>
    <!-- Add other Firebase SDKs as needed -->
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-auth-compat.js"></script>

<link rel="stylesheet" href="progress-styles.css">
</head>
<body class="bg-black text-white home-tab-active" data-theme="dark">
    <div id="app" class="min-h-screen">
        <!-- Authentication Modal -->
        <div id="authModal" class="fixed inset-0 bg-black/90 z-[100] flex items-center justify-center hidden">
            <div class="bg-gray-900 p-6 rounded-xl w-full max-w-md mx-4">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-bold gradient-logo">Picasso</h2>
                    <button onclick="closeAuthModal()" class="p-1 rounded-full hover:bg-gray-700">
                        <i class="fas fa-times text-lg"></i>
                    </button>
                </div>
                
                <!-- Login Form -->
                <div id="loginForm">
                    <h3 class="text-lg font-semibold mb-4">Log in to your account</h3>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium mb-1">Email</label>
                            <input type="email" id="loginEmail" class="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-pink-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-1">Password</label>
                            <input type="password" id="loginPassword" class="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-pink-500">
                        </div>
                        <button onclick="login()" class="w-full bg-gradient-to-r from-pink-500 to-purple-600 text-white py-2 rounded-lg font-medium hover:from-pink-600 hover:to-purple-700 transition-all">
                            Log In
                        </button>
                        <div class="text-center text-sm">
                            <a href="#" onclick="showSignupForm()" class="text-pink-400 hover:underline">Don't have an account? Sign up</a>
                        </div>
                    </div>
                </div>
                
                <!-- Signup Form (Initially Hidden) -->
                <div id="signupForm" class="hidden">
                    <h3 class="text-lg font-semibold mb-4">Create an account</h3>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium mb-1">Username</label>
                            <input type="text" id="signupUsername" class="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-pink-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-1">Email</label>
                            <input type="email" id="signupEmail" class="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-pink-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-1">Password</label>
                            <input type="password" id="signupPassword" class="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-pink-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-1">Confirm Password</label>
                            <input type="password" id="signupConfirmPassword" class="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-pink-500">
                        </div>
                        <button onclick="signup()" class="w-full bg-gradient-to-r from-pink-500 to-purple-600 text-white py-2 rounded-lg font-medium hover:from-pink-600 hover:to-purple-700 transition-all">
                            Sign Up
                        </button>
                        <div class="text-center text-sm">
                            <a href="#" onclick="showLoginForm()" class="text-pink-400 hover:underline">Already have an account? Log in</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Messages Panel (Initially Hidden) -->
        <div id="messagesPanel" class="fixed inset-0 bg-black/90 z-[100] flex hidden messages-panel">
            <div class="bg-gray-900 w-full h-full max-w-md border-r border-gray-800">
                <div class="p-4 border-b border-gray-800">
                    <div class="flex justify-between items-center">
                        <h3 class="font-semibold">Messages</h3>
                        <button onclick="closeMessagesPanel()" class="p-1 rounded-full hover:bg-gray-700">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="mt-3">
                        <input type="text" placeholder="Search messages..." class="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-pink-500">
                    </div>
                </div>
                <div class="overflow-y-auto" style="height: calc(100% - 80px);">
                    <!-- Chat List -->
                    <div class="chat-item p-3 border-b border-gray-800 cursor-pointer hover:bg-gray-800" onclick="openChat('user1')">
                        <div class="flex items-center">
                            <img src="https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=50&h=50&fit=crop" 
                                alt="User" class="w-12 h-12 rounded-full">
                            <div class="ml-3 flex-1">
                                <div class="flex justify-between">
                                    <span class="font-semibold">@artista_maya</span>
                                    <span class="text-xs text-gray-400">2m</span>
                                </div>
                                <p class="text-sm text-gray-400 truncate">Would love to commission a piece!</p>
                            </div>
                        </div>
                    </div>
                    <div class="chat-item p-3 border-b border-gray-800 cursor-pointer hover:bg-gray-800" onclick="openChat('user2')">
                        <div class="flex items-center">
                            <img src="https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=50&h=50&fit=crop" 
                                alt="User" class="w-12 h-12 rounded-full">
                            <div class="ml-3 flex-1">
                                <div class="flex justify-between">
                                    <span class="font-semibold">@creative_soul</span>
                                    <span class="text-xs text-gray-400">1h</span>
                                </div>
                                <p class="text-sm text-gray-400 truncate">Great job on your latest piece!</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div id="activeChatPanel" class="flex-1 flex flex-col">
                <div class="p-4 border-b border-gray-800 flex items-center">
                    <img src="https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=50&h=50&fit=crop" 
                         alt="User" class="w-10 h-10 rounded-full">
                    <div class="ml-3">
                        <span class="font-semibold" id="activeChatUser">@artista_maya</span>
                        <p class="text-xs text-gray-400">Digital Artist</p>
                    </div>
                </div>
                <div class="flex-1 overflow-y-auto p-4" id="chatMessages">
                    <!-- Chat Messages -->
                    <div class="message-item mb-4">
                        <div class="flex items-start">
                            <img src="https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=30&h=30&fit=crop" 
                                 alt="User" class="w-8 h-8 rounded-full">
                            <div class="ml-2 bg-gray-800 px-4 py-2 rounded-lg rounded-tl-none">
                                <p class="text-sm">Hi there! I love your artwork.</p>
                                <span class="text-xs text-gray-400">10:30 AM</span>
                            </div>
                        </div>
                    </div>
                    <div class="message-item mb-4 flex justify-end">
                        <div class="bg-gradient-to-r from-pink-500 to-purple-600 px-4 py-2 rounded-lg rounded-tr-none">
                            <p class="text-sm">Thank you so much! What can I help you with?</p>
                            <span class="text-xs text-right block">10:32 AM</span>
                        </div>
                    </div>
                    <div class="message-item mb-4">
                        <div class="flex items-start">
                            <img src="https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=30&h=30&fit=crop" 
                                 alt="User" class="w-8 h-8 rounded-full">
                            <div class="ml-2 bg-gray-800 px-4 py-2 rounded-lg rounded-tl-none">
                                <p class="text-sm">Would love to commission a piece from you. What's your rate?</p>
                                <span class="text-xs text-gray-400">10:35 AM</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="p-4 border-t border-gray-800">
                    <div class="flex items-center">
                        <input type="text" placeholder="Type a message..." class="flex-1 bg-gray-800 border border-gray-700 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-pink-500">
                        <button class="ml-2 p-2 bg-pink-500 rounded-lg">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- TikTok-style Overlay Navigation (Home page only) -->
        <div class="fixed top-0 left-0 right-0 z-50 home-only-nav">
            <!-- Top Navigation Overlay -->
            <div class="flex items-center justify-between w-full px-6 py-4 pt-8">
                <!-- Live button -->
                <button class="nav-filter-btn tiktok-nav-btn relative" data-filter="live">
                    <div class="flex items-center">
                        <div class="live-dot"></div>
                        <span class="text-gray-400 font-medium text-sm md:text-base">Live</span>
                    </div>
                    <div class="nav-underline live-underline"></div>
                </button>

                <!-- Friends button -->
                <button class="nav-filter-btn tiktok-nav-btn relative" data-filter="friends">
                    <span class="text-gray-400 font-medium text-sm md:text-base">Friends</span>
                    <div class="nav-underline"></div>
                </button>

                <!-- Artists button -->
                <button class="nav-filter-btn tiktok-nav-btn relative" data-filter="artists">
                    <span class="text-gray-400 font-medium text-sm md:text-base">Artists</span>
                    <div class="nav-underline"></div>
                </button>

                <!-- Feed button -->
                <button class="nav-filter-btn tiktok-nav-btn relative active" data-filter="feed">
                    <span class="text-white font-medium text-sm md:text-base">Feed</span>
                    <div class="nav-underline"></div>
                </button>

                <!-- Busking button -->
                <button class="nav-filter-btn tiktok-nav-btn relative" data-filter="busking">
                    <span class="text-gray-400 font-medium text-sm md:text-base">Busking</span>
                    <div class="nav-underline"></div>
                </button>

                <!-- Search button -->


                <button class="tiktok-nav-btn hover:scale-105 transition-all" onclick="toggleSearch()">
                    <i class="fas fa-search text-gray-400 text-base md:text-lg"></i>
                </button>
            </div>
        </div>

        <!-- Search Overlay (Initially Hidden) -->
        <div id="searchOverlay" class="fixed inset-0 bg-black/90 z-[90] flex items-start justify-center pt-20 hidden search-overlay">
            <div class="bg-gray-900 p-6 rounded-xl w-full max-w-2xl mx-4">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-bold">Search</h2>
                    <button onclick="closeSearch()" class="p-1 rounded-full hover:bg-gray-700">
                        <i class="fas fa-times text-lg"></i>
                    </button>
                </div>
                
                <div class="relative mb-6">
                    <i class="fas fa-search absolute top-3 left-3 text-gray-400"></i>
                    <input type="text" id="searchInput" placeholder="Search for artists, artwork, tags..." 
                           class="w-full bg-gray-800 border border-gray-700 rounded-lg pl-10 pr-3 py-2 focus:outline-none focus:ring-2 focus:ring-pink-500">
                </div>

                <div class="mb-4">
                    <h3 class="text-sm font-medium text-gray-400 mb-2">Filter by Category</h3>
                    <div class="flex flex-wrap">
                        <div class="category-pill active" data-category="all" onclick="filterByCategory('all')">All</div>
                        <div class="category-pill" data-category="painting" onclick="filterByCategory('painting')">Painting</div>
                        <div class="category-pill" data-category="digital" onclick="filterByCategory('digital')">Digital Art</div>
                        <div class="category-pill" data-category="photography" onclick="filterByCategory('photography')">Photography</div>
                        <div class="category-pill" data-category="sculpture" onclick="filterByCategory('sculpture')">Sculpture</div>
                        <div class="category-pill" data-category="music" onclick="filterByCategory('music')">Music</div>
                        <div class="category-pill" data-category="busking" onclick="filterByCategory('busking')">Busking</div>
                    </div>
                </div>

                <div class="mb-4">
                    <h3 class="text-sm font-medium text-gray-400 mb-2">Price Range</h3>
                    <div class="flex items-center space-x-3">
                        <input type="number" placeholder="Min" class="w-24 bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-sm">
                        <span>-</span>
                        <input type="number" placeholder="Max" class="w-24 bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-sm">
                        <button class="px-3 py-1 bg-gray-700 rounded-lg text-sm hover:bg-gray-600">Apply</button>
                    </div>
                </div>

                <div>
                    <h3 class="text-sm font-medium text-gray-400 mb-2">Sort By</h3>
                    <select class="bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-sm w-full">
                        <option value="newest">Newest</option>
                        <option value="popular">Most Popular</option>
                        <option value="price_low">Price: Low to High</option>
                        <option value="price_high">Price: High to Low</option>
                    </select>
                </div>

                <div class="mt-6">
                    <h3 class="text-lg font-semibold mb-4">Results</h3>
                    <div class="grid grid-cols-2 gap-4">
                        <!-- Search results will appear here -->
                        <div class="search-result bg-gray-800 rounded-lg overflow-hidden">
                            <div class="aspect-square bg-gradient-to-br from-purple-900 to-blue-900"></div>
                            <div class="p-2">
                                <p class="text-sm font-medium">Abstract Dreams</p>
                                <div class="flex justify-between items-center">
                                    <span class="text-xs text-gray-400">@artista_maya</span>
                                    <span class="text-xs font-medium">₱8,500</span>
                                </div>
                            </div>
                        </div>
                        <div class="search-result bg-gray-800 rounded-lg overflow-hidden">
                            <div class="aspect-square bg-gradient-to-br from-green-900 to-teal-900"></div>
                            <div class="p-2">
                                <p class="text-sm font-medium">Ocean Melody</p>
                                <div class="flex justify-between items-center">
                                    <span class="text-xs text-gray-400">@creative_soul</span>
                                    <span class="text-xs font-medium">₱12,000</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Shopping Cart Panel (Initially Hidden) -->
        <div id="shoppingCartPanel" class="fixed inset-0 bg-black/90 z-[90] flex items-start justify-center pt-20 hidden shopping-cart-panel">
            <div class="bg-gray-900 p-6 rounded-xl w-full max-w-2xl mx-4">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-bold">Shopping Cart</h2>
                    <button onclick="closeShoppingCart()" class="p-1 rounded-full hover:bg-gray-700">
                        <i class="fas fa-times text-lg"></i>
                    </button>
                </div>
                
                <div id="cartEmptyMessage" class="py-8 text-center text-gray-400">
                    <i class="fas fa-shopping-cart text-3xl mb-3"></i>
                    <p>Your cart is empty</p>
                </div>
                
                <div id="cartItems" class="hidden">
                    <div class="max-h-96 overflow-y-auto">
                        <div class="space-y-4" id="cartItemsList">
                            <!-- Cart items will be added here dynamically -->
                        </div>
                    </div>
                    
                    <div class="mt-6 border-t border-gray-800 pt-4">
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-sm text-gray-400">Subtotal</span>
                            <span id="cartSubtotal" class="font-medium">₱0</span>
                        </div>
                        <div class="flex justify-between items-center mb-4">
                            <span class="text-sm text-gray-400">Shipping</span>
                            <span class="font-medium">₱150</span>
                        </div>
                        <div class="flex justify-between items-center mb-6 text-lg font-bold">
                            <span>Total</span>
                            <span id="cartTotal">₱150</span>
                        </div>
                        
                        <button onclick="proceedToCheckout()" class="w-full bg-gradient-to-r from-pink-500 to-purple-600 text-white py-3 rounded-lg font-medium hover:from-pink-600 hover:to-purple-700 transition-all">
                            Proceed to Checkout
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Checkout Panel (Initially Hidden) -->
        <div id="checkoutPanel" class="fixed inset-0 bg-black/90 z-[90] flex items-start justify-center pt-20 hidden checkout-panel">
            <div class="bg-gray-900 p-6 rounded-xl w-full max-w-2xl mx-4">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-bold">Checkout</h2>
                    <button onclick="backToCart()" class="p-1 rounded-full hover:bg-gray-700">
                        <i class="fas fa-arrow-left text-lg"></i>
                    </button>
                </div>
                
                <div class="mb-6">
                    <h3 class="text-lg font-semibold mb-3">Shipping Details</h3>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium mb-1">First Name</label>
                            <input type="text" class="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-pink-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-1">Last Name</label>
                            <input type="text" class="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-pink-500">
                        </div>
                    </div>
                    <div class="mt-3">
                        <label class="block text-sm font-medium mb-1">Address</label>
                        <input type="text" class="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-pink-500">
                    </div>
                    <div class="grid grid-cols-2 gap-4 mt-3">
                        <div>
                            <label class="block text-sm font-medium mb-1">City</label>
                            <input type="text" class="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-pink-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-1">Postal Code</label>
                            <input type="text" class="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-pink-500">
                        </div>
                    </div>
                    <div class="mt-3">
                        <label class="block text-sm font-medium mb-1">Phone Number</label>
                        <input type="text" class="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-pink-500">
                    </div>
                </div>
                
                <div class="mb-6">
                    <h3 class="text-lg font-semibold mb-3">Order Summary</h3>
                    <div class="bg-gray-800 rounded-lg p-4">
                        <div id="checkoutItemsList" class="space-y-2 mb-4">
                            <!-- Checkout items summary will be added here -->
                        </div>
                        <div class="border-t border-gray-700 pt-3">
                            <div class="flex justify-between items-center mb-1">
                                <span class="text-sm text-gray-400">Subtotal</span>
                                <span id="checkoutSubtotal" class="font-medium">₱0</span>
                            </div>
                            <div class="flex justify-between items-center mb-1">
                                <span class="text-sm text-gray-400">Shipping</span>
                                <span class="font-medium">₱150</span>
                            </div>
                            <div class="flex justify-between items-center text-lg font-bold">
                                <span>Total</span>
                                <span id="checkoutTotal">₱150</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-3">Payment Method</h3>
                    <div class="grid grid-cols-3 gap-3 mb-4">
                        <button class="p-3 bg-gray-800 rounded-lg border border-pink-500 flex items-center justify-center">
                            <i class="fab fa-cc-visa text-2xl"></i>
                        </button>
                        <button class="p-3 bg-gray-800 rounded-lg border border-gray-700 flex items-center justify-center">
                            <i class="fab fa-cc-mastercard text-2xl"></i>
                        </button>
                        <button class="p-3 bg-gray-800 rounded-lg border border-gray-700 flex items-center justify-center">
                            <i class="fab fa-paypal text-2xl"></i>
                        </button>
                    </div>
                    <div>
                        <label class="block text-sm font-medium mb-1">Card Number</label>
                        <input type="text" placeholder="1234 5678 9012 3456" class="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-pink-500">
                    </div>
                    <div class="grid grid-cols-2 gap-4 mt-3">
                        <div>
                            <label class="block text-sm font-medium mb-1">Expiry Date</label>
                            <input type="text" placeholder="MM/YY" class="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-pink-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-1">CVC</label>
                            <input type="text" placeholder="123" class="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-pink-500">
                        </div>
                    </div>
                    
                    <button onclick="completeOrder()" class="w-full bg-gradient-to-r from-pink-500 to-purple-600 text-white py-3 rounded-lg font-medium hover:from-pink-600 hover:to-purple-700 transition-all mt-6">
                        Complete Order
                    </button>
                </div>
            </div>
        </div>

        <!-- Favorites Collection (Initially Hidden) -->
        <div id="favoritesPanel" class="fixed inset-0 bg-black/90 z-[90] flex items-start justify-center pt-20 hidden favorites-panel">
            <div class="bg-gray-900 p-6 rounded-xl w-full max-w-2xl mx-4">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-bold">Favorites Collection</h2>
                    <button onclick="closeFavorites()" class="p-1 rounded-full hover:bg-gray-700">
                        <i class="fas fa-times text-lg"></i>
                    </button>
                </div>
                
                <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                    <div class="favorite-item bg-gray-800 rounded-lg overflow-hidden">
                        <div class="aspect-square bg-gradient-to-br from-purple-900 to-blue-900 relative">
                            <button class="absolute top-2 right-2 p-1 bg-black/50 rounded-full" onclick="removeFromFavorites(this)">
                                <i class="fas fa-heart text-pink-500"></i>
                            </button>
                        </div>
                        <div class="p-2">
                            <p class="text-sm font-medium">Abstract Dreams</p>
                            <div class="flex justify-between items-center">
                                <span class="text-xs text-gray-400">@artista_maya</span>
                                <span class="text-xs font-medium">₱8,500</span>
                            </div>
                        </div>
                    </div>
                    <div class="favorite-item bg-gray-800 rounded-lg overflow-hidden">
                        <div class="aspect-square bg-gradient-to-br from-green-900 to-teal-900 relative">
                            <button class="absolute top-2 right-2 p-1 bg-black/50 rounded-full" onclick="removeFromFavorites(this)">
                                <i class="fas fa-heart text-pink-500"></i>
                            </button>
                        </div>
                        <div class="p-2">
                            <p class="text-sm font-medium">Ocean Melody</p>
                            <div class="flex justify-between items-center">
                                <span class="text-xs text-gray-400">@creative_soul</span>
                                <span class="text-xs font-medium">₱12,000</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bottom Slide Panel (TikTok-style) -->
        <div id="bottomSlidePanel" class="fixed inset-0 bg-black/50 z-[100] hidden" onclick="closeBottomSlidePanel(event)">
            <div id="bottomSlideContent" class="absolute bottom-0 left-0 right-0 bg-gray-900 rounded-t-3xl transform translate-y-full transition-transform duration-300 ease-out">
                <!-- Handle bar -->
                <div class="flex justify-center pt-3 pb-2">
                    <div class="w-12 h-1 bg-gray-600 rounded-full"></div>
                </div>

                <!-- Panel Content -->
                <div class="px-6 pb-8 pt-6">
                    <!-- Grid of options -->
                    <div class="grid grid-cols-4 gap-6 mt-6">
                        <!-- Marketplace -->
                        <div class="flex flex-col items-center" onclick="switchToMarketplace()">
                            <div class="w-12 h-12 bg-gray-800 rounded-xl flex items-center justify-center mb-2 hover:bg-gray-700 transition-colors">
                                <i class="fas fa-shopping-bag text-white text-lg"></i>
                            </div>
                            <span class="text-xs text-gray-300 text-center">Marketplace</span>
                        </div>

                        <!-- My Shop -->
                        <div class="flex flex-col items-center" onclick="switchToMyShop()">
                            <div class="w-12 h-12 bg-gray-800 rounded-xl flex items-center justify-center mb-2 hover:bg-gray-700 transition-colors">
                                <i class="fas fa-store-alt text-white text-lg"></i>
                            </div>
                            <span class="text-xs text-gray-300 text-center">My Shop</span>
                        </div>

                        <!-- Orders -->
                        <div class="flex flex-col items-center" onclick="openOrdersModal()">
                            <div class="w-12 h-12 bg-gray-800 rounded-xl flex items-center justify-center mb-2 hover:bg-gray-700 transition-colors">
                                <i class="fas fa-box text-white text-lg"></i>
                            </div>
                            <span class="text-xs text-gray-300 text-center">Orders</span>
                        </div>

                        <!-- Coupons/Vouchers -->
                        <div class="flex flex-col items-center" onclick="openVouchersModal()">
                            <div class="w-12 h-12 bg-gray-800 rounded-xl flex items-center justify-center mb-2 hover:bg-gray-700 transition-colors">
                                <i class="fas fa-ticket-alt text-white text-lg"></i>
                            </div>
                            <span class="text-xs text-gray-300 text-center">Coupons</span>
                        </div>

                        <!-- Messages -->
                        <div class="flex flex-col items-center" onclick="openMessagesModal()">
                            <div class="w-12 h-12 bg-gray-800 rounded-xl flex items-center justify-center mb-2 hover:bg-gray-700 transition-colors">
                                <i class="fas fa-comment text-white text-lg"></i>
                            </div>
                            <span class="text-xs text-gray-300 text-center">Messages</span>
                        </div>

                        <!-- Bonus/Rewards -->
                        <div class="flex flex-col items-center" onclick="openBonusModal()">
                            <div class="w-12 h-12 bg-gray-800 rounded-xl flex items-center justify-center mb-2 hover:bg-gray-700 transition-colors">
                                <i class="fas fa-gift text-white text-lg"></i>
                            </div>
                            <span class="text-xs text-gray-300 text-center">Bonus</span>
                        </div>

                        <!-- Sell -->
                        <div class="flex flex-col items-center" onclick="openSellModal()">
                            <div class="w-12 h-12 bg-gray-800 rounded-xl flex items-center justify-center mb-2 hover:bg-gray-700 transition-colors">
                                <i class="fas fa-store text-white text-lg"></i>
                            </div>
                            <span class="text-xs text-gray-300 text-center">Sell</span>
                        </div>

                        <!-- Address -->
                        <div class="flex flex-col items-center" onclick="openAddressModal()">
                            <div class="w-12 h-12 bg-gray-800 rounded-xl flex items-center justify-center mb-2 hover:bg-gray-700 transition-colors">
                                <i class="fas fa-map-marker-alt text-white text-lg"></i>
                            </div>
                            <span class="text-xs text-gray-300 text-center">Address</span>
                        </div>

                        <!-- Payment -->
                        <div class="flex flex-col items-center" onclick="openPaymentModal()">
                            <div class="w-12 h-12 bg-gray-800 rounded-xl flex items-center justify-center mb-2 hover:bg-gray-700 transition-colors">
                                <i class="fas fa-credit-card text-white text-lg"></i>
                            </div>
                            <span class="text-xs text-gray-300 text-center">Payment</span>
                        </div>

                        <!-- Help -->
                        <div class="flex flex-col items-center" onclick="openHelpModal()">
                            <div class="w-12 h-12 bg-gray-800 rounded-xl flex items-center justify-center mb-2 hover:bg-gray-700 transition-colors">
                                <i class="fas fa-question-circle text-white text-lg"></i>
                            </div>
                            <span class="text-xs text-gray-300 text-center">Help</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <main class="h-screen">

    <!-- Home Tab Content -->
    <div id="home-content" class="relative tab-content home-tab-active h-full">
        <div class="block max-w-md mx-auto h-full">
            <div class="scrollbar-hide h-full overflow-y-auto snap-y snap-mandatory">
            <!-- Video Card 1 -->
            <div class="snap-start w-full h-screen relative">
            <div class="relative video-card-container w-full h-full bg-gray-900" data-category="painting">
                <div class="w-full h-full bg-gradient-to-br from-purple-900 to-blue-900 flex items-center justify-center">
                  <i class="fas fa-play text-6xl text-white/50 cursor-pointer"></i>
                   <!--container for product elements-->
                    <div class="absolute left-4 bottom-24 flex flex-col items-start space-y-3">
                        <!-- Product Tag -->
                          <div class="product-tag bottom-4 rounded-full px-3 py-1 text-xs cursor-pointer flex items-center">
                            <div onclick="viewLinkedProducts(this)">
                                <i class="fas fa-shopping-bag mr-1"></i>
                                Oil Painting - ₱8,500
                            </div>
                            <button class="ml-2 add-to-cart-btn p-1 bg-pink-500 rounded-full" 
                                    data-product="Abstract Dreams Oil Painting" 
                                    data-price="8500"
                                    data-image="https://via.placeholder.com/100">
                                <i class="fas fa-cart-plus text-xs"></i>
                            </button>
                        </div>
                        <!-- 2 Products Linked Button -->
                        <div class="flex items-center space-x-2">
                            <button class="bg-gray-800 p-1 rounded-lg" onclick="viewLinkedProducts(this)">
                                <i class="fas fa-shopping-cart text-yellow-400 text-lg mr-1"></i>
                                <span class="text-xs text-gray-300">2 products linked</span>
                            </button>
                        </div>
                        <!--maya's info-->
                        <div class="w-full pr-6">
                            <div class="flex items-center justify-between mb-2">
                                <div class="flex items-center space-x-3">
                                    <img src="https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=50&h=50&fit=crop" 
                                         alt="Artist" class="w-10 h-10 rounded-full">
                                    <div>
                                        <div class="flex items-center space-x-2">
                                            <span class="font-semibold cursor-pointer" onclick="viewArtistPortfolio('artista_maya')">@artista_maya</span>
                                            <button class="follow-btn bg-gradient-to-r from-pink-500 to-purple-600 text-white px-3 py-1 rounded-full text-xs font-medium hover:from-pink-600 hover:to-purple-700 transition-all" onclick="toggleFollow(this)">
                                                Follow
                                            </button>
                                        </div>
                                        <div class="text-sm text-gray-400">Digital Artist</div>
                                    </div>
                                </div>
                            </div>
                            <p class="text-sm text-gray-300">Creating magic with colors ✨ #art #painting #creative</p>
                            
                    </div>
              </div>           
                        <!-- TikTok-style interaction buttons on right side -->
                        <div class="absolute right-3 bottom-32 flex flex-col items-center space-y-3">
                            <!-- Brush button -->
                            <div class="text-center">
                                <button class="action-btn p-2 sm:p-3 md:p-4 rounded-full bg-white/10 backdrop-blur hover:scale-105 transition-all brush-btn" onclick="toggleBrush(this)" data-count="2100">
                                    <i class="fas fa-paint-brush text-xl text-gray-400"></i>
                                </button>
                                <div class="text-xs text-gray-400 brush-count">2.1K</div>
                            </div>
                            
                            <!-- Comment button -->
                            <div class="text-center">
                                <button  class="action-btn p-2 sm:p-3 md:p-4 rounded-full bg-white/10 backdrop-blur hover:scale-105 transition-all comment-btn" onclick="toggleComments(this)" data-count="89">
                                    <i class="fas fa-comment text-xl"></i>
                                </button>
                                <div class="text-xs text-gray-400 comment-count">89</div>
                            </div>
                            
                            <!-- Bid Button (yellow if auction available) -->
                            <div class="text-center">
                                <button class="action-btn p-2 sm:p-3 md:p-4 rounded-full bg-white/10 backdrop-blur hover:scale-105 transition-all bid-btn" onclick="showBidModal(this)" data-auction="true">
                                    <i class="fas fa-gavel text-xl bid-available"></i>
                                </button>
                                <div class="text-xs text-gray-400">Bid</div>
                            </div>
                            
                            <!-- Tip Button -->
                            <div class="text-center">
                                <button class="action-btn p-2 sm:p-3 md:p-4 rounded-full bg-white/10 backdrop-blur hover:scale-105 transition-all tip-btn" onclick="showTipModal(this)" data-artist="@artista_maya">
                                    <i class="fas fa-coins text-xl text-gray-400"></i>
                                </button>
                                <div class="text-xs text-gray-400">Tip</div>
                            </div>
                            <!-- Share button -->
                            <div class="text-center">
                                <button class="action-btn p-2 sm:p-3 md:p-4 rounded-full bg-white/10 backdrop-blur hover:scale-105 transition-all share-btn" onclick="shareVideo(this)" data-count="34">
                                    <i class="fas fa-share text-xl"></i>
                                </button>
                                <div class="text-xs text-gray-400 share-count">34</div>
                            </div>
                        </div>
                             <!-- Comments Sidebar (Hidden off-screen by default) -->
                             <div class="comments-section absolute top-0 right-0 h-full w-[350px] bg-gray-800 transform translate-x-full transition-transform duration-300 ease-in-out p-4 shadow-lg z-[60]">
                                <div class="max-h-[80%] overflow-y-auto space-y-2 mb-3">
                                    <div class="flex items-start space-x-2">
                                        <img src="https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=30&h=30&fit=crop" alt="User" class="w-6 h-6 rounded-full">
                                        <div>
                                            <span class="text-white text-sm font-medium">@user1</span>
                                            <p class="text-gray-300 text-sm">Amazing artwork! 🎨</p>
                                        </div>
                                    </div>
                                    <div class="flex items-start space-x-2">
                                        <img src="https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=30&h=30&fit=crop" alt="User" class="w-6 h-6 rounded-full">
                                        <div>
                                            <span class="text-white text-sm font-medium">@user2</span>
                                            <p class="text-gray-300 text-sm">Love the colors!</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <input type="text" placeholder="Add a comment..." class="flex-1 bg-gray-700 text-white px-3 py-2 rounded-full text-sm focus:outline-none focus:ring-2 focus:ring-pink-500 comment-input">
                                    <button class="bg-pink-500 text-white px-4 py-2 rounded-full text-sm hover:bg-pink-600 transition-colors" onclick="addComment(this)">Post</button>
                                </div>
                                <button onclick="closeComments(this)" class="absolute top-4 right-4 text-white hover:text-pink-400">
                                    <i class="fas fa-times text-xl"></i>
                                </button>
                            </div>

                        </div>
                    </div>
            </div>
                    <!-- Video Card 2 (Busking Category) -->
                    <div class="snap-start relative w-full h-screen">
                    <div class="relative video-card-container flex w-full h-full bg-gray-900 overflow-hidden busking-item" data-category="busking">
                        <div class="relative w-full h-full bg-gradient-to-br from-green-900 to-teal-900 flex items-center justify-center relative">
                            <i class="fas fa-play text-6xl text-white/50 cursor-pointer"></i>
                            <!-- Live Badge -->
                            <div class="absolute top-6 left-4 live-badge px-3 py-1 rounded-full text-xs font-bold">
                                <i class="fas fa-circle text-xs mr-1"></i>LIVE
                            </div>
                            <!--container for product elements-->
                            <div class="absolute left-4 bottom-24 flex flex-col items-start space-y-3">
                                <!-- Product Tag -->
                                <div class="product-tag rounded-full px-6 py-2 text-xs cursor-pointer flex items-center bg-black/70 text-white">
                                    <div onclick="viewLinkedProducts(this)">
                                        <i class="fas fa-music mr-1"></i>
                                        Guitar Session - ₱50/song
                                    </div>
                                    <button class="ml-2 add-to-cart-btn p-1 bg-pink-500 rounded-full" 
                                            data-product="Guitar Session" 
                                            data-price="50"
                                            data-image="https://via.placeholder.com/100">
                                        <i class="fas fa-cart-plus text-xs text-white"></i>
                                    </button>
                                </div>
                                <!--Guitar Master's Info-->
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center space-x-3">
                                            <img src="https://images.pexels.com/photos/1043471/pexels-photo-1043471.jpeg?auto=compress&cs=tinysrgb&w=50&h=50&fit=crop" 
                                                 alt="Musician" class="w-10 h-10 rounded-full">
                                            <div>
                                                <div class="flex items-center space-x-2">
                                                    <span class="font-semibold">@guitar_master</span>
                                                    <button class="follow-btn bg-gradient-to-r from-pink-500 to-purple-600 text-white px-3 py-1 rounded-full text-xs font-medium hover:from-pink-600 hover:to-purple-700 transition-all" onclick="toggleFollow(this)">
                                                        Follow
                                                    </button>
                                                </div>
                                                <div class="text-sm text-gray-400">Street Musician</div>
                                            </div>
                                        </div>
                                    </div>
                                    <p class="text-sm text-gray-300">Live guitar session! Request your favorite songs 🎸 #music #live #guitar</p>
                            </div>
                        </div>
                        </div>
                        <div class="absolute right-3 bottom-32 flex flex-col items-center space-y-3">
                            <div class="text-center">
                                <button class="action-btn p-2 sm:p-3 md:p-4 rounded-full bg-white/10 backdrop-blur hover:scale-105 transition-all brush-btn" onclick="toggleBrush(this)" data-count="5700">
                                    <i class="fas fa-paint-brush text-xl text-gray-400"></i>
                                </button>
                                <div class="text-xs text-gray-400 brush-count">5.7K</div>
                            </div>
                            
                            <div class="text-center">
                                <button class="action-btn p-2 sm:p-3 md:p-4 rounded-full bg-white/10 backdrop-blur hover:scale-105 transition-all brush-btn" onclick="toggleComments(this)" data-count="234">
                                    <i class="fas fa-comment text-xl"></i>
                                </button>
                                <div class="text-xs text-gray-400 comment-count">234</div>
                            </div>
                            
                            <div class="text-center">
                                <button class="action-btn p-2 sm:p-3 md:p-4 rounded-full bg-white/10 backdrop-blur hover:scale-105 transition-all brush-btn" onclick="showBidModal(this)" data-auction="false">
                                    <i class="fas fa-gavel text-xl text-gray-400"></i>
                                </button>
                                <div class="text-xs text-gray-400">Bid</div>
                            </div>
                            
                            <!-- Tip Button for Busking (Yellow when busking is active) -->
                            <div class="text-center">
                                <button class="action-btn p-2 sm:p-3 md:p-4 rounded-full bg-white/10 backdrop-blur hover:scale-105 transition-all brush-btn" onclick="showTipModal(this)" data-artist="@guitar_master">
                                    <i class="fas fa-coins text-xl tip-available"></i>
                                </button>
                                <div class="text-xs text-gray-400">Tip</div>
                            </div>
                            
                            <div class="text-center">
                                <button class="action-btn p-2 sm:p-3 md:p-4 rounded-full bg-white/10 backdrop-blur hover:scale-105 transition-all brush-btn" onclick="shareVideo(this)" data-count="156">
                                    <i class="fas fa-share text-xl"></i>
                                </button>
                                <div class="text-xs text-gray-400 share-count">156</div>
                            </div>
                        </div>
                            <!-- Comments Section (Hidden by default) -->
                            <div class="comments-section absolute top-0 right-0 h-full w-[320px] bg-gray-800 transform translate-x-full transition-transform duration-300 ease-in-out p-4 shadow-lg z-[60]">
                                <div class="max-h-[80%] overflow-y-auto space-y-2 mb-3">                        
                                    <div class="flex items-start space-x-2">
                                        <img src="https://images.pexels.com/photos/1043471/pexels-photo-1043471.jpeg?auto=compress&cs=tinysrgb&w=30&h=30&fit=crop" alt="User" class="w-6 h-6 rounded-full">
                                        <div>
                                            <span class="text-white text-sm font-medium">@fan1</span>
                                            <p class="text-gray-300 text-sm">Great performance! 🎸</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <input type="text" placeholder="Add a comment..." class="flex-1 bg-gray-700 text-white px-3 py-2 rounded-full text-sm focus:outline-none focus:ring-2 focus:ring-pink-500 comment-input">
                                    <button class="bg-pink-500 text-white px-4 py-2 rounded-full text-sm hover:bg-pink-600 transition-colors" onclick="addComment(this)">Post</button>
                                </div>
                                <button onclick="closeComments(this)" class="absolute top-4 right-4 text-white hover:text-pink-400">
                                    <i class="fas fa-times text-xl"></i>
                                </button>
                            </div>
                    
                    </div>
                    </div>
                    </div>
                  </div>
                
                  <!-- Shop Tab Content -->
            <div id="shop-content" class="tab-content hidden pb-24">
                <!-- TikTok-style Fixed Search Bar with Matching Gradient -->
                <div class="sticky top-0 z-40 shop-top-nav bg-gradient-to-b from-purple-500 via-purple-400 to-purple-400">
                    <!-- Search Bar Section - Fixed at top -->
                    <div class="flex items-center justify-between px-4 py-3">
                        <!-- Search Bar -->
                        <div class="flex-1 relative mr-3">
                            <input type="text" id="shop-search-input" placeholder="Search products..."
                                   class="w-full bg-white/20 backdrop-blur-sm border border-white/30 rounded-full px-4 py-2 pl-10 pr-4 text-white placeholder-white/70 focus:outline-none focus:border-white/50 text-sm">
                            <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-white/70 text-sm"></i>
                        </div>

                        <!-- Cart Button -->
                        <button onclick="toggleShoppingCart()" class="relative p-2 mr-2 transition-colors bg-white/20 backdrop-blur-sm rounded-lg hover:bg-white/30">
                            <i class="fas fa-shopping-cart text-white text-lg"></i>
                            <span id="cartCountBadge" class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center hidden">0</span>
                        </button>

                        <!-- Hamburger Menu Button -->
                        <button onclick="toggleBottomSlidePanel()" class="p-2 transition-colors bg-white/20 backdrop-blur-sm rounded-lg hover:bg-white/30">
                            <i class="fas fa-bars text-white text-lg"></i>
                        </button>
                    </div>
                </div>

                <!-- Scrollable Content Area with Continuing Gradient -->
                <div class="relative bg-gradient-to-b from-purple-400 via-purple-300 to-blue-300">
                    <!-- TikTok-style Carousel Section - Scrollable -->
                    <div class="px-4 py-3">
                        <div class="relative overflow-hidden rounded-lg">
                            <div id="carousel-container" class="flex transition-transform duration-500 ease-in-out">
                                <!-- Carousel Items -->
                                <div class="carousel-item flex-shrink-0 w-full px-2">
                                    <div class="bg-gradient-to-r from-pink-500 to-purple-600 rounded-lg p-4">
                                        <div class="text-white font-bold text-lg mb-1">🎨 Picasso Top 100</div>
                                        <div class="text-white/90 text-sm">Masterpiece Collection</div>
                                    </div>
                                </div>
                                <div class="carousel-item flex-shrink-0 w-full px-2">
                                    <div class="bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg p-4">
                                        <div class="text-white font-bold text-lg mb-1">🎭 Top 100 Buskers</div>
                                        <div class="text-white/90 text-sm">Street Art Performers</div>
                                    </div>
                                </div>
                                <div class="carousel-item flex-shrink-0 w-full px-2">
                                    <div class="bg-gradient-to-r from-green-500 to-teal-500 rounded-lg p-4">
                                        <div class="text-white font-bold text-lg mb-1">🌍 Top 100 Global</div>
                                        <div class="text-white/90 text-sm">Worldwide Artists</div>
                                    </div>
                                </div>
                                <div class="carousel-item flex-shrink-0 w-full px-2">
                                    <div class="bg-gradient-to-r from-orange-500 to-red-500 rounded-lg p-4">
                                        <div class="text-white font-bold text-lg mb-1">🔥 Top 100 Viral Creators</div>
                                        <div class="text-white/90 text-sm">Trending Artists</div>
                                    </div>
                                </div>
                                <div class="carousel-item flex-shrink-0 w-full px-2">
                                    <div class="bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg p-4">
                                        <div class="text-white font-bold text-lg mb-1">💰 Top 100 Sulit Deals</div>
                                        <div class="text-white/90 text-sm">Best Value Offers</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Pagination Dots -->
                            <div class="flex justify-center mt-3 space-x-2">
                                <div class="carousel-dot w-2 h-2 rounded-full bg-white/50 transition-all duration-300" data-index="0"></div>
                                <div class="carousel-dot w-2 h-2 rounded-full bg-white/50 transition-all duration-300" data-index="1"></div>
                                <div class="carousel-dot w-2 h-2 rounded-full bg-white/50 transition-all duration-300" data-index="2"></div>
                                <div class="carousel-dot w-2 h-2 rounded-full bg-white/50 transition-all duration-300" data-index="3"></div>
                                <div class="carousel-dot w-2 h-2 rounded-full bg-white/50 transition-all duration-300" data-index="4"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Marketplace Content -->
                <div id="marketplace-content" class="shop-tab-content">
                    <!-- Categories -->
                    <div class="mb-6 mt-8">
                        <div class="relative">
                            
                            <!-- Categories Container -->
                            <div class="categories-container overflow-x-auto scrollbar-hide px-5" id="categoriesContainer">
                                <div class="flex space-x-2 pb-2 pt-2 min-w-max">
                                    <button class="category-btn bg-purple-600 text-white px-4 py-2 rounded-full text-sm whitespace-nowrap transition-all hover:scale-105" data-category="all">All</button>
                                    <button class="category-btn bg-gray-700 text-gray-300 px-4 py-2 rounded-full text-sm whitespace-nowrap transition-all hover:scale-105" data-category="digital-art">Digital Art</button>
                                    <button class="category-btn bg-gray-700 text-gray-300 px-4 py-2 rounded-full text-sm whitespace-nowrap transition-all hover:scale-105" data-category="painting">Painting</button>
                                    <button class="category-btn bg-gray-700 text-gray-300 px-4 py-2 rounded-full text-sm whitespace-nowrap transition-all hover:scale-105" data-category="sculptures">Sculptures</button>
                                    <button class="category-btn bg-gray-700 text-gray-300 px-4 py-2 rounded-full text-sm whitespace-nowrap transition-all hover:scale-105" data-category="art-materials">Art Materials</button>

                                    <!-- Art Services Dropdown -->
                                    <div class="relative inline-block">
                                        <button class="category-btn bg-gray-700 text-gray-300 px-4 py-2 rounded-full text-sm whitespace-nowrap flex items-center space-x-2 transition-all hover:scale-105" data-category="art-services" onclick="toggleArtServicesDropdown()">
                                            <span>Art Services</span>
                                            <i class="fas fa-chevron-down text-sm"></i>
                                        </button>
                                        <!-- Art Services Dropdown Menu -->
                                        <div id="artServicesDropdown" class="category-dropdown bg-gray-800 rounded-lg shadow-lg min-w-[200px] hidden">
                                            <div class="py-2">
                                                <button class="w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white transition-colors" data-subcategory="commissions">Commissions</button>
                                                <button class="w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white transition-colors" data-subcategory="tattoo">Tattoo Services</button>
                                                <button class="w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white transition-colors" data-subcategory="photography">Photography</button>
                                                <button class="w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white transition-colors" data-subcategory="design">Graphic Design</button>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Music Services Dropdown -->
                                    <div class="relative inline-block">
                                        <button class="category-btn bg-gray-700 text-gray-300 px-4 py-2 rounded-full text-sm whitespace-nowrap flex items-center space-x-2 transition-all hover:scale-105" data-category="music-services" onclick="toggleMusicServicesDropdown()">
                                            <span>Music Services</span>
                                            <i class="fas fa-chevron-down text-sm"></i>
                                        </button>
                                        <!-- Music Services Dropdown Menu -->
                                        <div id="musicServicesDropdown" class="category-dropdown bg-gray-800 rounded-lg shadow-lg min-w-[200px] hidden">
                                            <div class="py-2">
                                                <button class="w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white transition-colors" data-subcategory="busking">Busking</button>
                                                <button class="w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white transition-colors" data-subcategory="lessons">Music Lessons</button>
                                                <button class="w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white transition-colors" data-subcategory="recording">Recording Services</button>
                                                <button class="w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white transition-colors" data-subcategory="mixing">Audio Mixing</button>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Musical Instruments Dropdown -->
                                    <div class="relative inline-block">
                                        <button type="button" onclick="toggleInstrumentsDropdown()" class="category-btn bg-gray-700 text-gray-300 px-4 py-2 rounded-full text-sm whitespace-nowrap flex items-center space-x-2 transition-all hover:scale-105" data-category="instruments">
                                            <span>Musical Instruments</span>
                                            <i class="fas fa-chevron-down text-sm"></i>
                                        </button>
                                        <!-- Musical Instruments Dropdown Menu -->
                                        <div id="instrumentsDropdown" class="category-dropdown bg-gray-800 rounded-lg shadow-lg min-w-[200px] hidden">
                                            <div class="py-2">
                                                <button class="w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white transition-colors" data-subcategory="guitar">Guitars</button>
                                                <button class="w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white transition-colors" data-subcategory="piano">Piano/Keyboards</button>
                                                <button class="w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white transition-colors" data-subcategory="drums">Drums</button>
                                                <button class="w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white transition-colors" data-subcategory="violin">Violin/Strings</button>
                                                <button class="w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white transition-colors" data-subcategory="wind">Wind Instruments</button>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Electronics Category -->
                                    <button class="category-btn bg-gray-700 text-gray-300 px-4 py-2 rounded-full text-sm whitespace-nowrap transition-all hover:scale-105" data-category="electronics">Electronics</button>

                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Products Grid -->
                     <!-- px-4 if cp, sm(small), md(medium), xl(xtralarge)-->
                    <div class="px-4 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-5 gap-4 pb-24">
                        <!-- Product Card 1 -->
                        <div class="bg-gray-900 rounded-xl overflow-hidden product-card" data-category="digital-art">
                            <div class="aspect-square bg-gradient-to-br from-purple-600 to-pink-600 relative">
                                <img src="https://images.pexels.com/photos/1266808/pexels-photo-1266808.jpeg?auto=compress&cs=tinysrgb&w=300&h=300&fit=crop" 
                                     alt="Digital Art" class="w-full h-full object-cover">
                                <div class="absolute top-2 right-2 bg-green-500 text-white px-2 py-1 rounded-full text-xs">
                                    <i class="fas fa-gavel mr-1"></i>Auction
                                </div>
                            </div>
                            <div class="p-3">
                                <h3 class="font-semibold text-sm mb-1">Digital Portrait NFT</h3>
                                <p class="text-gray-400 text-xs mb-2">by @artista_maya</p>
                                <div class="flex items-center justify-between mb-3">
                                    <span class="text-purple-400 font-bold">₱12,500.00</span>
                                    <span class="text-xs text-gray-500">23 bids</span>
                                </div>
                                <div class="flex space-x-2">
                                    <button class="buy-btn flex-1 py-2 rounded-lg text-sm font-medium">
                                        <i class="fas fa-shopping-cart mr-1"></i>Buy Now
                                    </button>
                                    <button class="bg-gray-700 px-3 py-2 rounded-lg" onclick="openChat()">
                                        <i class="fas fa-comment text-sm"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Product Card 2 -->
                        <div class="bg-gray-900 rounded-xl overflow-hidden product-card" data-category="painting">
                            <div class="aspect-square bg-gradient-to-br from-blue-600 to-teal-600 relative">
                                <img src="https://images.pexels.com/photos/1183992/pexels-photo-1183992.jpeg?auto=compress&cs=tinysrgb&w=300&h=300&fit=crop" 
                                     alt="Oil Painting" class="w-full h-full object-cover">
                                <div class="absolute top-2 right-2 bg-blue-500 text-white px-2 py-1 rounded-full text-xs">
                                    Fixed Price
                                </div>
                            </div>
                            <div class="p-3">
                                <h3 class="font-semibold text-sm mb-1">Oil Painting - Sunset</h3>
                                <p class="text-gray-400 text-xs mb-2">by @painter_pro</p>
                                <div class="flex items-center justify-between mb-3">
                                    <span class="text-purple-400 font-bold">₱8,500.00</span>
                                    <span class="text-xs text-green-500">In Stock</span>
                                </div>
                                <div class="flex space-x-2">
                                    <button class="buy-btn flex-1 py-2 rounded-lg text-sm font-medium">
                                        <i class="fas fa-shopping-cart mr-1"></i>Buy Now
                                    </button>
                                    <button class="bg-gray-700 px-3 py-2 rounded-lg" onclick="openChat()">
                                        <i class="fas fa-comment text-sm"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Busking Service -->
                        <div class="bg-gray-900 rounded-xl overflow-hidden product-card" data-category="busking">
                            <div class="aspect-square bg-gradient-to-br from-yellow-600 to-orange-600 relative">
                                <img src="https://images.pexels.com/photos/1407322/pexels-photo-1407322.jpeg?auto=compress&cs=tinysrgb&w=300&h=300&fit=crop" 
                                     alt="Street Guitar Performance" class="w-full h-full object-cover">
                                <div class="absolute top-2 right-2 live-badge px-2 py-1 rounded-full text-xs">
                                    <i class="fas fa-circle text-xs mr-1"></i>LIVE
                                </div>
                            </div>
                            <div class="p-3">
                                <h3 class="font-semibold text-sm mb-1">Street Guitar Performance</h3>
                                <p class="text-gray-400 text-xs mb-2">by @guitar_master</p>
                                <div class="flex items-center justify-between mb-3">
                                    <span class="text-purple-400 font-bold">₱50/song</span>
                                    <span class="text-xs text-yellow-500">Busking</span>
                                </div>
                                <div class="flex space-x-2">
                                    <button class="buy-btn flex-1 py-2 rounded-lg text-sm font-medium">
                                        <i class="fas fa-video mr-1"></i>Join Live
                                    </button>
                                    <button class="bg-gray-700 px-3 py-2 rounded-lg" onclick="openChat()">
                                        <i class="fas fa-comment text-sm"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Service Cards -->
                        <div class="bg-gray-900 rounded-xl overflow-hidden product-card" data-category="services" data-subcategory="graphic-design">
                            <div class="aspect-square bg-gradient-to-br from-indigo-600 to-purple-600 relative">
                                <img src="https://images.pexels.com/photos/196644/pexels-photo-196644.jpeg?auto=compress&cs=tinysrgb&w=300&h=300&fit=crop" 
                                     alt="Graphic Design Service" class="w-full h-full object-cover">
                            </div>
                            <div class="p-3">
                                <h3 class="font-semibold text-sm mb-1">Logo & Brand Design</h3>
                                <p class="text-gray-400 text-xs mb-2">by @designer_pro</p>
                                <div class="flex items-center justify-between mb-3">
                                    <span class="text-purple-400 font-bold">₱2,500.00</span>
                                    <span class="text-xs text-green-500">Available</span>
                                </div>
                                <div class="flex space-x-2">
                                    <button class="buy-btn flex-1 py-2 rounded-lg text-sm font-medium">
                                        <i class="fas fa-shopping-cart mr-1"></i>Buy Now
                                    </button>
                                    <button class="bg-gray-700 px-3 py-2 rounded-lg" onclick="openChat()">
                                        <i class="fas fa-comment text-sm"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="bg-gray-900 rounded-xl overflow-hidden product-card" data-category="services" data-subcategory="commissions">
                            <div class="aspect-square bg-gradient-to-br from-pink-600 to-red-600 relative">
                                <img src="https://images.pexels.com/photos/1266808/pexels-photo-1266808.jpeg?auto=compress&cs=tinysrgb&w=300&h=300&fit=crop" 
                                     alt="Art Commission" class="w-full h-full object-cover">
                            </div>
                            <div class="p-3">
                                <h3 class="font-semibold text-sm mb-1">Custom Portrait Commission</h3>
                                <p class="text-gray-400 text-xs mb-2">by @portrait_artist</p>
                                <div class="flex items-center justify-between mb-3">
                                    <span class="text-purple-400 font-bold">₱3,500.00</span>
                                    <span class="text-xs text-green-500">Available</span>
                                </div>
                                <div class="flex space-x-2">
                                    <button class="buy-btn flex-1 py-2 rounded-lg text-sm font-medium">
                                        <i class="fas fa-shopping-cart mr-1"></i>Buy Now
                                    </button>
                                    <button class="bg-gray-700 px-3 py-2 rounded-lg" onclick="openChat()">
                                        <i class="fas fa-comment text-sm"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="bg-gray-900 rounded-xl overflow-hidden product-card" data-category="services" data-subcategory="tattoo">
                            <div class="aspect-square bg-gradient-to-br from-gray-600 to-black relative">
                                <img src="https://images.pexels.com/photos/1319460/pexels-photo-1319460.jpeg?auto=compress&cs=tinysrgb&w=300&h=300&fit=crop" 
                                     alt="Tattoo Service" class="w-full h-full object-cover">
                            </div>
                            <div class="p-3">
                                <h3 class="font-semibold text-sm mb-1">Custom Tattoo Design</h3>
                                <p class="text-gray-400 text-xs mb-2">by @ink_master</p>
                                <div class="flex items-center justify-between mb-3">
                                    <span class="text-purple-400 font-bold">₱5,000.00</span>
                                    <span class="text-xs text-green-500">Available</span>
                                </div>
                                <div class="flex space-x-2">
                                    <button class="buy-btn flex-1 py-2 rounded-lg text-sm font-medium">
                                        <i class="fas fa-shopping-cart mr-1"></i>Buy Now
                                    </button>
                                    <button class="bg-gray-700 px-3 py-2 rounded-lg" onclick="openChat()">
                                        <i class="fas fa-comment text-sm"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Example Service Cards for Demo Purposes -->
                        <div class="bg-gray-900 rounded-xl overflow-hidden product-card" data-category="services" data-subcategory="guitar-luthier">
                            <div class="aspect-square bg-gradient-to-br from-gray-600 to-black relative">
                                <img src="https://hub.yamaha.com/wp-content/uploads/2015/05/DSC00195-min-1-scaled.jpg" 
                                     alt="Guitar Luthier Service" class="w-full h-full object-cover">
                            </div>
                            <div class="p-3">
                                <h3 class="font-semibold text-sm mb-1">Guitar Luthier Services</h3>
                                <p class="text-gray-400 text-xs mb-2">by @craftser</p>
                                <div class="flex items-center justify-between mb-3">
                                    <span class="text-purple-400 font-bold">₱2,000 - ₱20,000</span>
                                    <span class="text-xs text-green-500">Available</span>
                                </div>
                                <div class="flex space-x-2">
                                    <button class="buy-btn flex-1 py-2 rounded-lg text-sm font-medium" onclick="openLuthierForm()">
                                        <i class="fas fa-guitar mr-1"></i>Request Service
                                    </button>
                                    <button class="bg-gray-700 px-3 py-2 rounded-lg" onclick="openChat()">
                                        <i class="fas fa-comment text-sm"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="bg-gray-900 rounded-xl overflow-hidden product-card" data-category="services" data-subcategory="guitar-luthier">
                            <div class="aspect-square bg-gradient-to-br from-gray-600 to-black relative">
                                <img src="https://m.media-amazon.com/images/I/71Kxqh0AisL._AC_SL1500_.jpg" 
                                     alt="Violin Luthier Service" class="w-full h-full object-cover">
                            </div>
                            <div class="p-3">
                                <h3 class="font-semibold text-sm mb-1">Violin Luthier Services</h3>
                                <p class="text-gray-400 text-xs mb-2">by @violinist</p>
                                <div class="flex items-center justify-between mb-3">
                                    <span class="text-purple-400 font-bold">₱3,000 - ₱18,000</span>
                                    <span class="text-xs text-green-500">Available</span>
                                </div>
                                <div class="flex space-x-2">
                                    <button class="buy-btn flex-1 py-2 rounded-lg text-sm font-medium">
                                        <i class="fas fa-violin mr-1"></i>Request Service
                                    </button>
                                    <button class="bg-gray-700 px-3 py-2 rounded-lg" onclick="openChat()">
                                        <i class="fas fa-comment text-sm"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="bg-gray-900 rounded-xl overflow-hidden product-card" data-category="services" data-subcategory="guitar-luthier">
                            <div class="aspect-square bg-gradient-to-br from-gray-600 to-black relative">
                                <img src="https://www.solowayschool.com/wp-content/uploads/2024/07/Cello-1024x1024.webp" 
                                     alt="Cello Luthier Service" class="w-full h-full object-cover">
                            </div>
                            <div class="p-3">
                                <h3 class="font-semibold text-sm mb-1">Cello Luthier Services</h3>
                                <p class="text-gray-400 text-xs mb-2">by @repair_master</p>
                                <div class="flex items-center justify-between mb-3">
                                    <span class="text-purple-400 font-bold">₱4,000 - ₱22,000</span>
                                    <span class="text-xs text-green-500">Available</span>
                                </div>
                                <div class="flex space-x-2">
                                    <button class="buy-btn flex-1 py-2 rounded-lg text-sm font-medium">
                                        <i class="fas fa-music mr-1"></i>Request Service
                                    </button>
                                    <button class="bg-gray-700 px-3 py-2 rounded-lg" onclick="openChat()">
                                        <i class="fas fa-comment text-sm"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- My Shop Content -->
                <div id="myshop-content" class="shop-tab-content hidden">
                    <!-- Shop Stats -->
                    <div class="px-4 mb-6">
                        <div class="bg-gray-900 rounded-xl p-4">
                            <div class="flex items-center justify-between mb-4">
                                <h2 class="text-lg font-bold">My Shop Dashboard</h2>
                                <div class="text-right">
                                    <div class="text-2xl font-bold text-green-400">₱8,750.50</div>
                                    <div class="text-xs text-gray-400">Available Balance</div>
                                </div>
                            </div>
                            <div class="grid grid-cols-3 gap-4 text-center">
                                <div>
                                    <div class="text-xl font-bold text-purple-400 stat-number">47</div>
                                    <div class="text-xs text-gray-400">Products</div>
                                </div>
                                <div>
                                    <div class="text-xl font-bold text-blue-400">156</div>
                                    <div class="text-xs text-gray-400">Orders</div>
                                </div>
                                <div>
                                    <div class="text-xl font-bold text-green-400">₱12,450</div>
                                    <div class="text-xs text-gray-400">Total Sales</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="px-4 mb-6">
                        <div class="grid grid-cols-2 gap-4">
                            <button onclick="openAddProductModal()" class="bg-gradient-to-r from-purple-600 to-blue-600 p-4 rounded-xl text-center">
                                <i class="fas fa-plus text-2xl mb-2"></i>
                                <div class="font-semibold">Add Product</div>
                            </button>
                            <button class="bg-gray-800 p-4 rounded-xl text-center">
                                <i class="fas fa-chart-line text-2xl mb-2"></i>
                                <div class="font-semibold">Analytics</div>
                            </button>
                        </div>
                    </div>

                    <!-- My Products Grid -->
                    <div class="px-4 mb-6">
                        <div class="products-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(160px, 1fr)); gap: 15px; min-height: 200px;">
                            <!-- Sample user products -->
                            <div class="product-card" style="background: #1a1a1a; border-radius: 12px; overflow: hidden; border: 1px solid #333;">
                                <div style="position: relative;">
                                    <img src="https://images.pexels.com/photos/1266808/pexels-photo-1266808.jpeg?auto=compress&cs=tinysrgb&w=300&h=300&fit=crop" 
                                         alt="My Digital Art" style="width: 100%; height: 120px; object-fit: cover;">
                                    <div style="position: absolute; top: 8px; right: 8px; background: rgba(0,0,0,0.7); border-radius: 12px; padding: 4px 8px;">
                                        <span style="color: white; font-size: 10px; font-weight: bold;">SOLD</span>
                                    </div>
                                </div>
                                <div style="padding: 12px;">
                                    <h4 style="color: white; font-size: 14px; margin: 0 0 8px 0; font-weight: 600;">My Digital Portrait</h4>
                                    <p style="color: #ff6b6b; font-size: 16px; font-weight: bold; margin: 0 0 8px 0;">₱15,000</p>
                                    <div style="display: flex; gap: 6px;">
                                        <button style="flex: 1; padding: 8px; border: none; border-radius: 6px; background: #333; color: white; font-size: 11px; cursor: pointer;">Edit</button>
                                        <button style="padding: 8px; border: 1px solid #333; border-radius: 6px; background: transparent; color: white; font-size: 11px; cursor: pointer;">📊</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Wallet & Payments -->
                    <div class="px-4 mb-6">
                        <div class="bg-gray-900 rounded-xl p-4">
                            <h3 class="font-bold mb-4">Payment Methods</h3>
                            <div class="space-y-3">
                                <div class="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <i class="fab fa-paypal text-blue-500 text-xl"></i>
                                        <span>PayPal</span>
                                    </div>
                                    <span class="bg-blue-500 text-white px-2 py-1 rounded-full text-xs">Connected</span>
                                </div>
                                <div class="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <i class="fas fa-mobile-alt text-blue-500 text-xl"></i>
                                        <span>GCash</span>
                                    </div>
                                    <button class="bg-gray-600 text-white px-3 py-1 rounded-full text-xs">Setup</button>
                                </div>
                                <div class="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <i class="fab fa-bitcoin text-green-500 text-xl"></i>
                                        <span>USDT</span>
                                    </div>
                                    <button class="bg-gray-600 text-white px-3 py-1 rounded-full text-xs">Setup</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Logistics -->
                    <div class="px-4 mb-6">
                        <div class="bg-gray-900 rounded-xl p-4">
                            <h3 class="font-bold mb-4">Shipping Partners</h3>
                            <div class="space-y-3">
                                <div class="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                                    <div>
                                        <div class="font-semibold">LBC Express</div>
                                        <div class="text-xs text-gray-400">₱85 - ₱150</div>
                                    </div>
                                    <span class="bg-green-500 text-white px-2 py-1 rounded-full text-xs">Active</span>
                                </div>
                                <div class="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                                    <div>
                                        <div class="font-semibold">J&T Express</div>
                                        <div class="text-xs text-gray-400">₱65 - ₱120</div>
                                    </div>
                                    <button class="bg-gray-600 text-white px-3 py-1 rounded-full text-xs">Setup</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Add Product Modal -->
            <div id="addProductModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 1000; align-items: center; justify-content: center;">
                <div style="background: #1a1a1a; border-radius: 12px; padding: 24px; width: 90%; max-width: 600px; border: 1px solid #333;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h3 style="color: white; margin: 0;">Add New Product</h3>
                        <button onclick="closeAddProductModal()" style="background: none; border: none; color: #888; font-size: 24px; cursor: pointer;">&times;</button>
                    </div>
                    
                    <form id="addProductForm" style="display: flex; flex-direction: column; gap: 16px;">
                        <div>
                            <label style="color: #888; font-size: 12px; display: block; margin-bottom: 4px;">Product Title *</label>
                            <input type="text" id="productTitle" required style="width: 100%; padding: 12px; border: 1px solid #333; border-radius: 6px; background: #2a2a2a; color: white; font-size: 14px;">
                        </div>
                        
                        <div>
                            <label style="color: #888; font-size: 12px; display: block; margin-bottom: 4px;">Price (₱) *</label>
                            <input type="number" id="productPrice" required min="0" step="0.01" style="width: 100%; padding: 12px; border: 1px solid #333; border-radius: 6px; background: #2a2a2a; color: white; font-size: 14px;">
                        </div>
                        
                        <div>
                            <label style="color: #888; font-size: 12px; display: block; margin-bottom: 4px;">Category *</label>
                            <select id="productCategory" required style="width: 100%; padding: 12px; border: 1px solid #333; border-radius: 6px; background: #2a2a2a; color: white; font-size: 14px;">
                                <option value="">Select Category</option>
                                <option value="art-materials">Art Materials</option>
                                <option value="busking">Busking</option>
                                <option value="digital-art">Digital Art</option>
                                <option value="musical-instruments">Musical Instruments</option>
                                <option value="painting">Painting</option>
                                <option value="sculptures">Sculptures</option>
                                <option value="services">Services</option>
                            </select>
                        </div>
                        
                        <div>
                            <label style="color: #888; font-size: 12px; display: block; margin-bottom: 4px;">Product Image</label>
                            <input type="file" id="productImage" accept="image/*" style="width: 100%; padding: 12px; border: 1px solid #333; border-radius: 6px; background: #2a2a2a; color: white; font-size: 14px;">
                        </div>
                        
                        <div>
                            <label style="color: #888; font-size: 12px; display: block; margin-bottom: 4px;">Description</label>
                            <textarea id="productDescription" rows="3" style="width: 100%; padding: 12px; border: 1px solid #333; border-radius: 6px; background: #2a2a2a; color: white; font-size: 14px; resize: vertical;"></textarea>
                        </div>
                        
                        <div id="productConditionContainer" style="display: none;">
                            <label style="color: #888; font-size: 12px; display: block; margin-bottom: 4px;">Condition *</label>
                            <select id="productCondition" style="width: 100%; padding: 12px; border: 1px solid #333; border-radius: 6px; background: #2a2a2a; color: white; font-size: 14px;">
                                <option>Select Condition</option>
                                <option value="brand-new">Brand New</option>
                                <option value="secondhand">Secondhand</option>
                            </select>
                        </div>
                        
                        <div style="display: flex; gap: 12px; margin-top: 8px;">
                            <button type="button" onclick="closeAddProductModal()" style="flex: 1; padding: 12px; border: 1px solid #333; border-radius: 6px; background: transparent; color: white; cursor: pointer;">Cancel</button>
                            <button type="button" onclick="addProduct()" style="flex: 1; padding: 12px; border: none; border-radius: 6px; background: linear-gradient(45deg, #ff6b6b, #4ecdc4); color: white; font-weight: bold; cursor: pointer;">Add Product</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Create Tab Content -->
            <div id="create-content" class="tab-content hidden">
                <div class="max-w-md mx-auto px-4">
                    <div class="bg-gray-900 rounded-2xl p-6 text-center">
                        <div class="w-20 h-20 bg-gradient-to-br from-purple-600 to-pink-600 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-plus text-3xl"></i>
                        </div>
                        <h2 class="text-xl font-bold mb-2">Create Content</h2>
                        <p class="text-gray-400 mb-6">Share your art with the world</p>
                        
                        <div class="space-y-4">
                            <button class="w-full bg-gradient-to-r from-purple-600 to-pink-600 py-3 rounded-xl font-semibold" onclick="startRecording()">
                                <i class="fas fa-video mr-2"></i>Camera
                            </button>
                            <button class="w-full bg-gray-800 py-3 rounded-xl font-semibold" onclick="uploadVideo()">
                                <i class="fas fa-image mr-2"></i>Gallery
                            </button>
                            <button class="w-full bg-gray-800 py-3 rounded-xl font-semibold" onclick="goLive()">
                                <i class="fas fa-broadcast-tower mr-2"></i>Go Live
                            </button>
                        </div>

                        <!-- Product Linking Section -->
                        <div class="mt-6 p-4 bg-gray-800 rounded-xl">
                            <h3 class="font-semibold mb-3">Tag Products</h3>
                            <div class="space-y-2">
                                <div class="flex items-center justify-between p-2 bg-gray-700 rounded-lg">
                                    <div class="flex items-center space-x-2">
                                        <img src="https://images.pexels.com/photos/1266808/pexels-photo-1266808.jpeg?auto=compress&cs=tinysrgb&w=40&h=40&fit=crop" 
                                             alt="Product" class="w-8 h-8 rounded">
                                        <div>
                                            <div class="text-sm font-medium">Digital Portrait NFT</div>
                                            <div class="text-xs text-gray-400">₱12,500.00</div>
                                        </div>
                                    </div>
                                    <button class="bg-purple-600 px-3 py-1 rounded-full text-xs">Tag</button>
                                </div>
                                <div class="flex items-center justify-between p-2 bg-gray-700 rounded-lg">
                                    <div class="flex items-center space-x-2">
                                        <img src="https://images.pexels.com/photos/1183992/pexels-photo-1183992.jpeg?auto=compress&cs=tinysrgb&w=40&h=40&fit=crop" 
                                             alt="Product" class="w-8 h-8 rounded">
                                        <div>
                                            <div class="text-sm font-medium">Oil Painting - Sunset</div>
                                            <div class="text-xs text-gray-400">₱8,500.00</div>
                                        </div>
                                    </div>
                                    <button class="bg-gray-600 px-3 py-1 rounded-full text-xs">Tag</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Notifications Tab Content -->
            <div id="notifications-content" class="tab-content hidden">
                <div class="max-w-md mx-auto px-4">
                    <h2 class="text-xl font-bold mb-6">Notifications</h2>
                    
                    <div class="space-y-4">
                        <div class="bg-gray-900 rounded-xl p-4">
                            <div class="flex items-start space-x-3">
                                <div class="w-10 h-10 bg-gradient-to-br from-purple-600 to-pink-600 rounded-full flex items-center justify-center">
                                    <i class="fas fa-paint-brush text-sm"></i>
                                </div>
                                <div class="flex-1">
                                    <p class="text-sm"><span class="font-semibold">@artlover23</span> liked your painting</p>
                                    <p class="text-xs text-gray-400">2 minutes ago</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-gray-900 rounded-xl p-4">
                            <div class="flex items-start space-x-3">
                                <div class="w-10 h-10 bg-gradient-to-br from-green-600 to-teal-600 rounded-full flex items-center justify-center">
                                    <i class="fas fa-gavel text-sm"></i>
                                </div>
                                <div class="flex-1">
                                    <p class="text-sm">New bid on your <span class="font-semibold">Digital Portrait NFT</span></p>
                                    <p class="text-xs text-gray-400">5 minutes ago</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-gray-900 rounded-xl p-4">
                            <div class="flex items-start space-x-3">
                                <div class="w-10 h-10 bg-gradient-to-br from-blue-600 to-purple-600 rounded-full flex items-center justify-center">
                                    <i class="fas fa-user-plus text-sm"></i>
                                </div>
                                <div class="flex-1">
                                    <p class="text-sm"><span class="font-semibold">@newartist</span> started following you</p>
                                    <p class="text-xs text-gray-400">1 hour ago</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-gray-900 rounded-xl p-4">
                            <div class="flex items-start space-x-3">
                                <div class="w-10 h-10 bg-gradient-to-br from-yellow-600 to-orange-600 rounded-full flex items-center justify-center">
                                    <i class="fas fa-dollar-sign text-sm"></i>
                                </div>
                                <div class="flex-1">
                                    <p class="text-sm">Affiliate commission earned: <span class="font-semibold text-green-400">₱125.00</span></p>
                                    <p class="text-xs text-gray-400">3 hours ago</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profile Tab Content -->
            <div id="profile-content" class="tab-content hidden">
                <div class="max-w-md mx-auto px-4">
                    <!-- Profile Header -->
                    <div class="text-center mb-6">
                        <img src="https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&fit=crop" 
                             alt="Profile" class="w-20 h-20 rounded-full mx-auto mb-4">
                        <h2 class="text-xl font-bold">@artista_maya</h2>
                        <p class="text-gray-400">Digital Artist & Creator</p>
                        
                        <div class="flex justify-center space-x-8 mt-4">
                            <div class="text-center">
                                <div class="font-bold">47</div>
                                <div class="text-xs text-gray-400">Posts</div>
                            </div>
                            <div class="text-center">
                                <div class="font-bold">2.1K</div>
                                <div class="text-xs text-gray-400">Followers</div>
                            </div>
                            <div class="text-center">
                                <div class="font-bold">892</div>
                                <div class="text-xs text-gray-400">Following</div>
                            </div>
                        </div>
                    </div>

                    <!-- Profile Tabs -->
                    <div class="flex justify-center mb-6">
                        <div class="flex bg-gray-800 rounded-full p-1">
                            <button id="videos-tab" class="tab-active px-4 py-2 rounded-full text-sm font-medium">
                                <i class="fas fa-video mr-1"></i>Videos
                            </button>
                            <button id="liked-tab" class="tab-inactive px-4 py-2 rounded-full text-sm font-medium">
                                <i class="fas fa-heart mr-1"></i>Liked
                            </button>
                            <button id="bookmarks-tab" class="tab-inactive px-4 py-2 rounded-full text-sm font-medium">
                                <i class="fas fa-bookmark mr-1"></i>Saved
                            </button>
                        </div>
                    </div>

                    <!-- Videos Grid -->
                    <div id="videos-grid" class="grid grid-cols-3 gap-1 mb-6">
                        <div class="aspect-[9/16] bg-gradient-to-br from-purple-600 to-pink-600 rounded-lg relative">
                            <i class="fas fa-play absolute inset-0 flex items-center justify-center text-2xl text-white/70"></i>
                            <div class="absolute bottom-1 left-1 text-xs">2.1K</div>
                        </div>
                        <div class="aspect-[9/16] bg-gradient-to-br from-blue-600 to-teal-600 rounded-lg relative">
                            <i class="fas fa-play absolute inset-0 flex items-center justify-center text-2xl text-white/70"></i>
                            <div class="absolute bottom-1 left-1 text-xs">5.7K</div>
                        </div>
                        <div class="aspect-[9/16] bg-gradient-to-br from-green-600 to-yellow-600 rounded-lg relative">
                            <i class="fas fa-play absolute inset-0 flex items-center justify-center text-2xl text-white/70"></i>
                            <div class="absolute bottom-1 left-1 text-xs">892</div>
                        </div>
                        <div class="aspect-[9/16] bg-gradient-to-br from-red-600 to-pink-600 rounded-lg relative">
                            <i class="fas fa-play absolute inset-0 flex items-center justify-center text-2xl text-white/70"></i>
                            <div class="absolute bottom-1 left-1 text-xs">1.3K</div>
                        </div>
                        <div class="aspect-[9/16] bg-gradient-to-br from-indigo-600 to-purple-600 rounded-lg relative">
                            <i class="fas fa-play absolute inset-0 flex items-center justify-center text-2xl text-white/70"></i>
                            <div class="absolute bottom-1 left-1 text-xs">3.2K</div>
                        </div>
                        <div class="aspect-[9/16] bg-gradient-to-br from-pink-600 to-red-600 rounded-lg relative">
                            <i class="fas fa-play absolute inset-0 flex items-center justify-center text-2xl text-white/70"></i>
                            <div class="absolute bottom-1 left-1 text-xs">756</div>
                        </div>
                    </div>

                    <!-- Boost Section -->
                    <div class="bg-gray-900 rounded-xl p-4 mb-4">
                        <h3 class="font-bold mb-2">Boost Your Content</h3>
                        <p class="text-gray-400 text-sm mb-4">Increase your reach and get more views</p>
                        <div class="flex items-center justify-between">
                            <span class="text-sm">Starting at <span class="font-bold text-green-400">₱49</span></span>
                            <button class="boost-btn px-4 py-2 rounded-lg text-sm font-medium">
                                <i class="fas fa-rocket mr-1"></i>Boost Now
                            </button>
                        </div>
                    </div>

                    <!-- Chat Support -->
                    <div class="bg-gray-900 rounded-xl p-4">
                        <button class="w-full flex items-center justify-center space-x-2 py-3 bg-gray-800 rounded-lg">
                            <i class="fas fa-headset"></i>
                            <span>Chat Support</span>
                        </button>
                    </div>
                </div>
            </div>
        </main>

            <!-- Bottom Navigation -->
            <nav class="fixed bottom-0 left-0 right-0 bg-black/90 backdrop-blur-md border-t border-gray-800 justify-center">
                <div class="grid grid-cols-5 items-center py-1">
                    <button class="nav-btn flex flex-col items-center py-2 text-white w-full" data-tab="home">

                        <!-- Using original FontAwesome home icon -->
                        <i class="fas fa-home text-xl mb-1"></i>
                        <span class="text-xs">Home</span>
                    </button>
                    <button class="nav-btn flex flex-col items-center py-2 text-gray-400 w-full" data-tab="shop">
                        <i class="fas fa-store text-xl mb-1"></i>
                        <span class="text-xs">Shop</span>
                    </button>
                    <button class="nav-btn flex flex-col items-center py-2 text-gray-400 w-full" data-tab="create">
                        <div class="w-8 h-8 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg flex items-center justify-center mb-1">
                            <i class="fas fa-plus text-sm"></i>
                        </div>
                        <span class="text-xs">Create</span>
                    </button>
                    <button class="nav-btn flex flex-col items-center py-2 text-gray-400 w-full" data-tab="notifications">
                        <i class="fas fa-bell text-xl mb-1"></i>
                        <span class="text-xs">Notifications</span>
                    </button>
                    <button class="nav-btn flex flex-col items-center py-2 text-gray-400 w-full" data-tab="profile">
                        <i class="fas fa-user text-xl mb-1"></i>
                        <span class="text-xs">Profile</span>
                    </button>
                </div>
            </nav>
        </div>

        <!-- Camera Recording Modal -->
        <div id="cameraModal" class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 hidden">
            <div class="camera-modal">
                <div class="camera-header">
                    <button class="close-camera" onclick="closeCameraModal()">&times;</button>
                    <h3>Record Video</h3>
                    <button class="camera-flip" onclick="flipCamera()">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
                <div class="camera-container">
                    <video id="cameraPreview" autoplay muted playsinline></video>
                    <canvas id="recordingCanvas" style="display: none;"></canvas>
                </div>
                <div class="camera-controls">
                    <div class="recording-timer" id="recordingTimer" style="display: none;">00:00</div>
                    <div class="camera-buttons">
                        <button class="record-btn" id="recordBtn" onclick="toggleRecording()">
                            <div class="record-circle"></div>
                        </button>
                    </div>
                    <div class="camera-actions">
                        <button class="camera-action" onclick="addEffect()">
                            <i class="fas fa-magic"></i>
                        </button>
                        <button class="camera-action" onclick="addMusic()">
                            <i class="fas fa-music"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Gallery Upload Modal -->
        <div id="galleryModal" class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 hidden">
            <div class="absolute bottom-0 left-0 right-0 bg-gray-900 rounded-t-2xl max-h-[80vh] overflow-hidden modal-content">
                <div class="flex items-center justify-between p-4 border-b border-gray-700">
                    <h3>Upload from Gallery</h3>
                    <span class="close cursor-pointer text-2xl" onclick="closeGalleryModal()">&times;</span>
                </div>
                <div class="upload-area" onclick="document.getElementById('videoUpload').click()">
                    <i class="fas fa-cloud-upload-alt"></i>
                    <p>Tap to select video from gallery</p>
                    <small>MP4, MOV, AVI up to 100MB</small>
                </div>
                <input type="file" id="videoUpload" accept="video/*" style="display: none;" onchange="handleVideoUpload(event)">
                <div id="uploadPreview" style="display: none;">
                    <video id="uploadedVideo" controls style="width: 100%; max-height: 300px;"></video>
                    <div class="upload-actions">
                        <button class="bg-gray-600 px-4 py-2 rounded-lg" onclick="closeGalleryModal()">Cancel</button>
                        <button class="bg-purple-600 px-4 py-2 rounded-lg" onclick="processUploadedVideo()">Next</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Chat Modal -->
        <div id="chat-modal" class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 hidden">
            <div class="absolute bottom-0 left-0 right-0 bg-gray-900 rounded-t-2xl max-h-[80vh] overflow-hidden modal-content">
                <div class="flex items-center justify-between p-4 border-b border-gray-700">
                    <div class="flex items-center space-x-3">
                        <img src="https://images.pexels.com/photos/1043471/pexels-photo-1043471.jpeg?auto=compress&cs=tinysrgb&w=40&h=40&fit=crop" 
                             alt="Seller" class="w-8 h-8 rounded-full">
                        <div>
                            <div class="font-semibold text-sm">@guitar_master</div>
                            <div class="text-xs text-gray-400">Online</div>
                        </div>
                    </div>
                    <button id="close-chat" class="text-gray-400 hover:text-white">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="p-4 h-64 overflow-y-auto space-y-3">
                    <div class="flex justify-start">
                        <div class="bg-gray-800 rounded-2xl rounded-bl-md px-4 py-2 max-w-xs">
                            <p class="text-sm">Hi! Interested in the guitar lesson?</p>
                        </div>
                    </div>
                    <div class="flex justify-end">
                        <div class="bg-purple-600 rounded-2xl rounded-br-md px-4 py-2 max-w-xs">
                            <p class="text-sm">Yes! What's included in the session?</p>
                        </div>
                    </div>
                    <div class="flex justify-start">
                        <div class="bg-gray-800 rounded-2xl rounded-bl-md px-4 py-2 max-w-xs">
                            <p class="text-sm">I teach basic chords, strumming patterns, and can help with specific songs. ₱500 per hour!</p>
                        </div>
                    </div>
                </div>
                
                <div class="p-4 border-t border-gray-700">
                    <div class="flex space-x-2">
                        <input type="text" placeholder="Type a message..." 
                               class="flex-1 bg-gray-800 border border-gray-700 rounded-full px-4 py-2 text-sm focus:outline-none focus:border-purple-500">
                        <button class="bg-purple-600 p-2 rounded-full">
                            <i class="fas fa-paper-plane text-sm"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tip Modal -->
        <div id="tipModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden">
            <div class="bg-gray-800 rounded-lg p-6 w-80 mx-4">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-white text-lg font-medium">Send Tip</h3>
                    <button onclick="closeTipModal()" class="text-gray-400 hover:text-white">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="space-y-4">
                    <div class="grid grid-cols-3 gap-2">
                        <button class="tip-amount bg-gray-700 text-white py-2 rounded-lg hover:bg-pink-500 transition-colors" onclick="selectTipAmount(50)">₱50</button>
                        <button class="tip-amount bg-gray-700 text-white py-2 rounded-lg hover:bg-pink-500 transition-colors" onclick="selectTipAmount(100)">₱100</button>
                        <button class="tip-amount bg-gray-700 text-white py-2 rounded-lg hover:bg-pink-500 transition-colors" onclick="selectTipAmount(200)">₱200</button>
                    </div>
                    <input type="number" id="customTipAmount" placeholder="Custom amount" class="w-full bg-gray-700 text-white px-3 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500">
                    <button class="w-full bg-gradient-to-r from-pink-500 to-purple-600 text-white py-2 rounded-lg font-medium hover:from-pink-600 hover:to-purple-700 transition-all" onclick="sendTip()">
                        Send Tip
                    </button>
                </div>
            </div>
        </div>

        <!-- Bid Modal -->
        <div id="bidModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
            <div class="bg-gray-800 rounded-lg p-6 w-80 mx-4">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-white text-lg font-medium">Place Bid</h3>
                    <button onclick="closeBidModal()" class="text-gray-400 hover:text-white">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="space-y-4">
                    <div class="text-center">
                        <p class="text-gray-400 text-sm">Current Bid</p>
                        <p class="text-white text-2xl font-bold" id="currentBidAmount">₱8,500</p>
                    </div>
                    <input type="number" id="bidAmount" placeholder="Your bid amount" class="w-full bg-gray-700 text-white px-3 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500" min="8501">
                    <button class="w-full bg-gradient-to-r from-orange-500 to-red-600 text-white py-2 rounded-lg font-medium hover:from-orange-600 hover:to-red-700 transition-all" onclick="placeBid()">
                        Place Bid
                    </button>
                </div>
            </div>
        </div>

        <!-- Share Modal -->
        <div id="shareModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
            <div class="bg-gray-800 rounded-lg p-6 w-80 mx-4">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-white text-lg font-medium">Share Video</h3>
                    <button onclick="closeShareModal()" class="text-gray-400 hover:text-white">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="grid grid-cols-3 gap-4">
                    <button class="flex flex-col items-center space-y-2 p-3 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors" onclick="shareToSocial('facebook')">
                        <i class="fab fa-facebook text-blue-500 text-2xl"></i>
                        <span class="text-white text-sm">Facebook</span>
                    </button>
                    <button class="flex flex-col items-center space-y-2 p-3 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors" onclick="shareToSocial('twitter')">
                        <i class="fab fa-twitter text-blue-400 text-2xl"></i>
                        <span class="text-white text-sm">Twitter</span>
                    </button>
                    <button class="flex flex-col items-center space-y-2 p-3 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors" onclick="shareToSocial('instagram')">
                        <i class="fab fa-instagram text-pink-500 text-2xl"></i>
                        <span class="text-white text-sm">Instagram</span>
                    </button>
                </div>
                <div class="mt-4">
                    <input type="text" id="shareLink" value="https://picasso.app/video/123" class="w-full bg-gray-700 text-white px-3 py-2 rounded-lg text-sm" readonly>
                    <button class="w-full mt-2 bg-green-500 text-white py-2 rounded-lg text-sm hover:bg-green-600 transition-colors" onclick="copyLink()">
                        Copy Link
                    </button>
                </div>
            </div>
        </div>

        <!-- Linked Products Modal -->
        <div id="linkedProductsModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
            <div class="bg-gray-800 rounded-lg p-6 w-96 max-h-96 overflow-y-auto mx-4">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-white text-lg font-medium">Linked Products</h3>
                    <button onclick="closeLinkedProductsModal()" class="text-gray-400 hover:text-white">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="space-y-3">
                    <div class="flex items-center space-x-3 bg-gray-700 p-3 rounded-lg">
                        <img src="https://images.pexels.com/photos/1266808/pexels-photo-1266808.jpeg?auto=compress&cs=tinysrgb&w=60&h=60&fit=crop" alt="Product" class="w-12 h-12 rounded-lg object-cover">
                        <div class="flex-1">
                            <h4 class="text-white font-medium text-sm">Digital Art Print</h4>
                            <p class="text-gray-400 text-sm">₱1,250.00</p>
                        </div>
                        <button class="bg-pink-500 text-white px-3 py-1 rounded-lg text-sm hover:bg-pink-600 transition-colors">
                            Buy
                        </button>
                    </div>
                    <div class="flex items-center space-x-3 bg-gray-700 p-3 rounded-lg">
                        <img src="https://images.pexels.com/photos/1266808/pexels-photo-1266808.jpeg?auto=compress&cs=tinysrgb&w=60&h=60&fit=crop" alt="Product" class="w-12 h-12 rounded-lg object-cover">
                        <div class="flex-1">
                            <h4 class="text-white font-medium text-sm">Art Supplies Set</h4>
                            <p class="text-gray-400 text-sm">₱850.00</p>
                        </div>
                        <button class="bg-pink-500 text-white px-3 py-1 rounded-lg text-sm hover:bg-pink-600 transition-colors">
                            Buy
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Luthier Service Modal -->
        <div id="luthierModal" class="fixed inset-0 bg-black/80 z-[200] flex items-center justify-center hidden">
            <div class="bg-gray-900 p-6 rounded-xl w-full max-w-md mx-4">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-bold">Request Guitar Luthier Service</h2>
                    <button onclick="closeLuthierForm()" class="p-1 rounded-full hover:bg-gray-700">
                        <i class="fas fa-times text-lg"></i>
                    </button>
                </div>
                <form id="luthierServiceForm" class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium mb-1">Service Type</label>
                        <select id="luthierServiceType" class="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2">
                            <option value="repair">Repair</option>
                            <option value="build">Build</option>
                            <option value="custom">Custom</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium mb-1">Price Range (₱)</label>
                        <div class="flex space-x-2">
                            <input type="number" id="luthierPriceMin" placeholder="Min" class="w-1/2 bg-gray-800 border border-gray-700 rounded-lg px-3 py-2">
                            <input type="number" id="luthierPriceMax" placeholder="Max" class="w-1/2 bg-gray-800 border border-gray-700 rounded-lg px-3 py-2">
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium mb-1">Estimated Duration (days)</label>
                        <input type="number" id="luthierDurationDays" placeholder="e.g. 10" class="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2" min="1">
                        <span id="durationFormatted" class="block text-xs text-gray-400 mt-1">  </span>
                    </div>
                    <div>
                        <label class="block text-sm font-medium mb-1">Location</label>
                        <input type="text" id="luthierLocation" placeholder="City/Province" class="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2">
                    </div>
                    <button type="button" onclick="submitLuthierService()" class="w-full bg-gradient-to-r from-yellow-600 to-gray-800 text-white py-2 rounded-lg font-medium hover:from-yellow-700 hover:to-gray-900 transition-all">Submit Request</button>
                </form>
            </div>
        </div>

        <!-- Guitar Luthier Services List Modal -->
        <div id="luthierListModal" class="fixed inset-0 bg-black/80 z-[200] flex items-center justify-center hidden">
            <div class="bg-gray-900 p-8 rounded-xl w-full max-w-2xl mx-4">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold">Guitar Luthier Services</h2>
                    <button onclick="closeLuthierListModal()" class="p-1 rounded-full hover:bg-gray-700">
                        <i class="fas fa-times text-lg"></i>
                    </button>
                </div>
                <div class="space-y-6">
                    <div class="border-b border-gray-700 pb-4">
                        <h3 class="font-semibold text-lg mb-1">Guitar Luthier Services</h3>
                        <p class="text-gray-400 text-sm mb-1">Building, repair, or customization of guitars and other string instruments. We offer expert craftsmanship and quality service for all your stringed instrument needs.</p>
                        <span class="text-purple-400 font-bold">₱2,000 - ₱20,000</span>
                    </div>
                    <div class="border-b border-gray-700 pb-4">
                        <h3 class="font-semibold text-lg mb-1">Violin Luthier Services</h3>
                        <p class="text-gray-400 text-sm mb-1">Expert repair and customization for violins and other string instruments. Restore your instrument's sound and beauty with our professional luthier services.</p>
                        <span class="text-purple-400 font-bold">₱3,000 - ₱18,000</span>
                    </div>
                    <div>
                        <h3 class="font-semibold text-lg mb-1">Cello Luthier Services</h3>
                        <p class="text-gray-400 text-sm mb-1">Professional building, repair, and customization for cellos and other string instruments. Trust us to keep your cello in top playing condition.</p>
                        <span class="text-purple-400 font-bold">₱4,000 - ₱22,000</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Dropdowns moved to body level to prevent clipping -->
    <div id="servicesDropdown" class="fixed z-[99999] services-dropdown rounded-lg shadow-lg hidden bg-gray-800 border border-gray-700 w-56">
        <button class="service-subcategory w-full text-left px-3 py-2 text-white hover:bg-gray-700 rounded-none first:rounded-t-lg last:rounded-b-lg transition-colors" data-subcategory="art-class">Art Class</button>
        <button class="service-subcategory w-full text-left px-3 py-2 text-white hover:bg-gray-700 rounded-none first:rounded-t-lg last:rounded-b-lg transition-colors" data-subcategory="commissions">Commissions</button>
        <button class="service-subcategory w-full text-left px-3 py-2 text-white hover:bg-gray-700 rounded-none first:rounded-t-lg last:rounded-b-lg transition-colors" data-subcategory="drum-lesson">Drum Lesson</button>
        <button class="service-subcategory w-full text-left px-3 py-2 text-white hover:bg-gray-700 rounded-none first:rounded-t-lg last:rounded-b-lg transition-colors" data-subcategory="guitar-lesson">Guitar Lesson</button>
        <button class="service-subcategory w-full text-left px-3 py-2 text-white hover:bg-gray-700 rounded-none first:rounded-t-lg last:rounded-b-lg transition-colors" data-subcategory="guitar-luthier">Guitar Luthier Services</button>
        <button class="service-subcategory w-full text-left px-3 py-2 text-white hover:bg-gray-700 rounded-none first:rounded-t-lg last:rounded-b-lg transition-colors" data-subcategory="graphic-design">Graphic Design</button>
        <button class="service-subcategory w-full text-left px-3 py-2 text-white hover:bg-gray-700 rounded-none first:rounded-t-lg last:rounded-b-lg transition-colors" data-subcategory="makeup">Make Up</button>
        <button class="service-subcategory w-full text-left px-3 py-2 text-white hover:bg-gray-700 rounded-none first:rounded-t-lg last:rounded-b-lg transition-colors" data-subcategory="piano-lesson">Piano Lesson</button>
        <button class="service-subcategory w-full text-left px-3 py-2 text-white hover:bg-gray-700 rounded-none first:rounded-t-lg last:rounded-b-lg transition-colors" data-subcategory="saxophone-lesson">Saxophone Lesson</button>
        <button class="service-subcategory w-full text-left px-3 py-2 text-white hover:bg-gray-700 rounded-none first:rounded-t-lg last:rounded-b-lg transition-colors" data-subcategory="tattoo">Tattoo</button>
        <button class="service-subcategory w-full text-left px-3 py-2 text-white hover:bg-gray-700 rounded-none first:rounded-t-lg last:rounded-b-lg transition-colors" data-subcategory="violin-lesson">Violin Lesson</button>
        <button class="service-subcategory w-full text-left px-3 py-2 text-white hover:bg-gray-700 rounded-none first:rounded-t-lg last:rounded-b-lg transition-colors" data-subcategory="voice-lesson">Voice Lesson</button>
    </div>

    <div id="instrumentsConditionDropdown" class="fixed z-[99999] services-dropdown rounded-lg shadow-lg hidden bg-gray-800 border border-gray-700 w-56">
        <button class="instruments-subcategory w-full px-3 py-2 text-white hover:bg-gray-700 text-left rounded-none first:rounded-t-lg last:rounded-b-lg transition-colors" data-subcategory="brand-new">Brand New</button>
        <button class="instruments-subcategory w-full px-3 py-2 text-white hover:bg-gray-700 text-left rounded-none first:rounded-t-lg last:rounded-b-lg transition-colors" data-subcategory="second-hand">Second Hand</button>
    </div>



<script src="main-app-logic.js"></script>
<script src="shop-navigation-module.js"></script>
<script src="camera-integration.js"></script>
<script src="mobile-camera-handler.js"></script>
<script src="shop-crud-operations.js"></script>
<script src="carousel-script.js"></script>





</body>
</html>
  </body>
  </html>