function goToCameraTab() {
    // Use your existing function to open the camera modal
    if (typeof startRecording === "function") {
        startRecording();
    }
}

// 1. TikTok-style nav (overlay)
document.querySelectorAll('.nav-filter-btn').forEach(btn => {
    const filter = btn.getAttribute('data-filter');
    if (filter === 'busking' || filter === 'live') {
        btn.onclick = function(e) {
            e.preventDefault();
            goToCameraTab();
        };
    }
});
// 2. Mobile main nav (bottom bar)
document.querySelectorAll('.nav-btn').forEach(btn => {
    const tab = btn.getAttribute('data-tab');
    // Only override if it is busking or live
    if (tab === 'busking' || tab === 'live') {
        btn.onclick = function(e) {
            e.preventDefault();
            goToCameraTab();
        };
    }
});
// 3. In case you have any other busking/live navs, catch them by icon or text as fallback (optional)
document.querySelectorAll('button').forEach(btn => {
    if (
        btn.innerText.trim().toLowerCase().includes('busking') ||
        btn.innerText.trim().toLowerCase().includes('live')
    ) {
        btn.onclick = function(e) {
            e.preventDefault();
            goToCameraTab();
        };
    }
});