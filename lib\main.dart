import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:permission_handler/permission_handler.dart';

void main() {
  WidgetsFlutterBinding.ensureInitialized();
  runApp(const PicassoApp());
}

class PicassoApp extends StatelessWidget {
  const PicassoApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Picasso - Create & Sell Art',
      theme: ThemeData(
        primarySwatch: Colors.pink,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      home: const PicassoWebView(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class PicassoWebView extends StatefulWidget {
  const PicassoWebView({super.key});

  @override
  State<PicassoWebView> createState() => _PicassoWebViewState();
}

class _PicassoWebViewState extends State<PicassoWebView> {
  InAppWebViewController? webViewController;
  String htmlContent = '';

  @override
  void initState() {
    super.initState();
    _requestPermissions();
    _loadHtmlContent();
  }

  Future<void> _requestPermissions() async {
    // Request permissions individually for better error handling
    final permissions = [
      Permission.camera,
      Permission.microphone,
      Permission.storage,
      Permission.notification,
    ];

    for (final permission in permissions) {
      final status = await permission.request();
      if (status.isDenied) {
        print('Permission denied: $permission');
      } else if (status.isPermanentlyDenied) {
        print('Permission permanently denied: $permission');
        // Optionally show dialog to open app settings
      }
    }
  }

  Future<void> _loadHtmlContent() async {
    try {
      print('Loading HTML content...');

      // Load the main HTML file
      String mainHtml = await rootBundle.loadString('assets/progress.html');
      print('Main HTML loaded, length: ${mainHtml.length}');

      // Load all external JavaScript files
      String mainAppLogic = await rootBundle.loadString('assets/main-app-logic.js');
      String shopNavModule = await rootBundle.loadString('assets/shop-navigation-module.js');
      String cameraIntegration = await rootBundle.loadString('assets/camera-integration.js');
      String mobileCameraHandler = await rootBundle.loadString('assets/mobile-camera-handler.js');
      String shopCrudOps = await rootBundle.loadString('assets/shop-crud-operations.js');
      String carouselScript = await rootBundle.loadString('assets/carousel-script.js');
      String firebaseConfig = await rootBundle.loadString('assets/firebase-config.js');

      // Load external CSS file
      String progressStyles = await rootBundle.loadString('assets/progress-styles.css');
      print('All assets loaded successfully');

      // Replace external script references with inline scripts
      htmlContent = mainHtml
          // Replace Firebase config
          .replaceAll('<script type="module" src="firebase-config.js"></script>',
                     '<script type="module">$firebaseConfig</script>')
          // Replace local script references
          .replaceAll('<script src="main-app-logic.js"></script>',
                     '<script>$mainAppLogic</script>')
          .replaceAll('<script src="shop-navigation-module.js"></script>',
                     '<script>$shopNavModule</script>')
          .replaceAll('<script src="camera-integration.js"></script>',
                     '<script>$cameraIntegration</script>')
          .replaceAll('<script src="mobile-camera-handler.js"></script>',
                     '<script>$mobileCameraHandler</script>')
          .replaceAll('<script src="shop-crud-operations.js"></script>',
                     '<script>$shopCrudOps</script>')
          .replaceAll('<script src="carousel-script.js"></script>',
                     '<script>$carouselScript</script>')
          // Replace CSS reference
          .replaceAll('<link rel="stylesheet" href="progress-styles.css">',
                     '<style>$progressStyles</style>')
          // Add Content Security Policy and hide scrollbars
          .replaceAll('<head>', '''<head>
    <meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' data: https: http: blob:; img-src 'self' data: https: http: blob:; media-src 'self' data: https: http: blob:; connect-src 'self' https: http: ws: wss:;">
    <style>
      /* Hide scrollbars for all browsers */
      html, body {
        overflow-x: hidden;
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* Internet Explorer 10+ */
      }

      /* Hide scrollbars for WebKit browsers (Chrome, Safari, Edge) */
      html::-webkit-scrollbar,
      body::-webkit-scrollbar,
      *::-webkit-scrollbar {
        display: none;
        width: 0px;
        background: transparent;
      }

      /* Ensure content can still scroll */
      html, body {
        scroll-behavior: smooth;
      }

      /* Hide scrollbars for any scrollable containers */
      .overflow-auto::-webkit-scrollbar,
      .overflow-y-auto::-webkit-scrollbar,
      .overflow-x-auto::-webkit-scrollbar {
        display: none;
        width: 0px;
        background: transparent;
      }

      .overflow-auto,
      .overflow-y-auto,
      .overflow-x-auto {
        scrollbar-width: none;
        -ms-overflow-style: none;
      }
    </style>''');

      print('HTML content processed, final length: ${htmlContent.length}');
      print('Content preview: ${htmlContent.substring(0, 200)}...');

      setState(() {});
    } catch (e) {
      print('Error loading HTML content: $e');
      print('Stack trace: ${StackTrace.current}');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: htmlContent.isNotEmpty
            ? InAppWebView(
                initialData: InAppWebViewInitialData(
                  data: htmlContent,
                  baseUrl: WebUri("https://picasso-app.local/"),
                ),
                initialSettings: InAppWebViewSettings(
                  useShouldOverrideUrlLoading: true,
                  mediaPlaybackRequiresUserGesture: false,
                  allowsInlineMediaPlayback: true,
                  iframeAllow: "camera; microphone; geolocation",
                  iframeAllowFullscreen: true,
                  mixedContentMode: MixedContentMode.MIXED_CONTENT_ALWAYS_ALLOW,
                  allowsBackForwardNavigationGestures: false,
                  supportZoom: false,
                  disableHorizontalScroll: false,
                  disableVerticalScroll: false,
                  useWideViewPort: true,
                  loadWithOverviewMode: true,
                  javaScriptEnabled: true,
                  domStorageEnabled: true,
                  databaseEnabled: true,
                  clearCache: false,
                  cacheEnabled: true,
                  verticalScrollBarEnabled: false,
                  horizontalScrollBarEnabled: false,
                  // Camera and media specific settings
                  allowsAirPlayForMediaPlayback: true,
                  allowsPictureInPictureMediaPlayback: true,
                  allowsLinkPreview: true,
                  // Security settings for camera access and asset loading
                  allowUniversalAccessFromFileURLs: true,
                  allowFileAccessFromFileURLs: true,
                  // Additional settings to handle cleartext traffic
                  resourceCustomSchemes: ["flutter_assets"],


                ),
                onWebViewCreated: (controller) {
                  webViewController = controller;

                  // Add custom URL scheme handler for flutter_assets
                  controller.addWebMessageListener(WebMessageListener(
                    jsObjectName: "flutterAssetHandler",
                    onPostMessage: (message, sourceOrigin, isMainFrame, replyProxy) {
                      print('Message from WebView: $message');
                    },
                  ));
                },
                onLoadStart: (controller, url) {
                  // No loading state needed
                },
                onLoadStop: (controller, url) async {
                  // WebView loaded successfully


                },
                onReceivedError: (controller, request, error) {
                  print('=== WebView Error ===');
                  print('Error Description: ${error.description}');
                  print('Error URL: ${request.url}');
                  print('Error Type: ${error.type}');
                  print('Error Details: ${error.toString()}');
                  print('Request Method: ${request.method}');
                  print('Request Headers: ${request.headers}');
                  print('====================');
                },
                onReceivedHttpError: (controller, request, errorResponse) {
                  print('=== HTTP Error ===');
                  print('HTTP Status Code: ${errorResponse.statusCode}');
                  print('HTTP Error URL: ${request.url}');
                  print('HTTP Reason Phrase: ${errorResponse.reasonPhrase}');
                  print('Response Headers: ${errorResponse.headers}');
                  print('==================');
                },
                onPermissionRequest: (controller, request) async {
                  return PermissionResponse(
                    resources: request.resources,
                    action: PermissionResponseAction.GRANT,
                  );
                },
                shouldOverrideUrlLoading: (controller, navigationAction) async {
                  final uri = navigationAction.request.url;
                  print('=== Navigation Attempt ===');
                  print('URL: $uri');
                  print('Scheme: ${uri?.scheme}');
                  print('Host: ${uri?.host}');
                  print('Path: ${uri?.path}');
                  print('========================');

                  if (uri != null) {
                    // Allow flutter_assets URLs
                    if (uri.scheme == "http" && uri.host == "flutter_assets") {
                      print('Allowing flutter_assets URL: $uri');
                      return NavigationActionPolicy.ALLOW;
                    }

                    // Allow picasso-app.local URLs
                    if (uri.host.contains("picasso-app.local")) {
                      print('Allowing picasso-app.local URL: $uri');
                      return NavigationActionPolicy.ALLOW;
                    }

                    // Allow HTTPS CDN resources (Tailwind, FontAwesome, Firebase)
                    if (uri.scheme == "https" && (
                        uri.host.contains("cdn.tailwindcss.com") ||
                        uri.host.contains("cdnjs.cloudflare.com") ||
                        uri.host.contains("gstatic.com") ||
                        uri.host.contains("googleapis.com") ||
                        uri.host.contains("firebaseapp.com") ||
                        uri.host.contains("firebase.com")
                    )) {
                      print('Allowing external CDN URL: $uri');
                      return NavigationActionPolicy.ALLOW;
                    }

                    // Block other external HTTP/HTTPS URLs
                    if ((uri.scheme == "http" || uri.scheme == "https") &&
                        !uri.host.contains("picasso-app.local") &&
                        uri.host != "flutter_assets") {
                      print('Blocking external URL: $uri');
                      return NavigationActionPolicy.CANCEL;
                    }
                  }
                  print('Allowing URL by default: $uri');
                  return NavigationActionPolicy.ALLOW;
                },
                shouldInterceptRequest: (controller, request) async {
                  final url = request.url.toString();
                  print('=== Resource Request ===');
                  print('URL: $url');
                  print('Method: ${request.method}');
                  print('Headers: ${request.headers}');

                  // Intercept flutter_assets requests and redirect them
                  if (url.contains('flutter_assets/assets/')) {
                    final assetPath = url.split('flutter_assets/assets/').last;
                    print('Intercepting asset request for: $assetPath');

                    try {
                      final assetData = await rootBundle.load('assets/$assetPath');
                      final bytes = assetData.buffer.asUint8List();

                      // Determine content type based on file extension
                      String contentType = 'text/html';
                      if (assetPath.endsWith('.js')) {
                        contentType = 'application/javascript';
                      } else if (assetPath.endsWith('.css')) {
                        contentType = 'text/css';
                      } else if (assetPath.endsWith('.html')) {
                        contentType = 'text/html';
                      } else if (assetPath.endsWith('.png')) {
                        contentType = 'image/png';
                      } else if (assetPath.endsWith('.jpg') || assetPath.endsWith('.jpeg')) {
                        contentType = 'image/jpeg';
                      } else if (assetPath.endsWith('.svg')) {
                        contentType = 'image/svg+xml';
                      }

                      print('Serving asset $assetPath with content type: $contentType');
                      return WebResourceResponse(
                        contentType: contentType,
                        data: bytes,
                        headers: {
                          'Access-Control-Allow-Origin': '*',
                          'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
                          'Access-Control-Allow-Headers': '*',
                        },
                      );
                    } catch (e) {
                      print('Error loading asset $assetPath: $e');
                      print('Stack trace: ${StackTrace.current}');
                    }
                  }

                  print('Not intercepting request for: $url');
                  return null;
                },
              )
            : Container(
                color: Colors.black,
                // Show black screen while content loads (matches splash screen)
              ),
      ),
    );
  }
}