// TikTok-style Carousel Script
document.addEventListener('DOMContentLoaded', function() {
    const carouselContainer = document.getElementById('carousel-container');
    const carouselItems = document.querySelectorAll('.carousel-item');
    const carouselDots = document.querySelectorAll('.carousel-dot');

    if (!carouselContainer || carouselItems.length === 0) return;

    let currentIndex = 0;
    const totalItems = carouselItems.length;

    // Update dots indicator
    function updateDots() {
        carouselDots.forEach((dot, index) => {
            if (index === currentIndex) {
                dot.classList.remove('bg-white/50');
                dot.classList.add('bg-white', 'w-6');
            } else {
                dot.classList.remove('bg-white', 'w-6');
                dot.classList.add('bg-white/50');
            }
        });
    }

    // Move to specific slide
    function goToSlide(index) {
        currentIndex = index;
        const containerWidth = carouselContainer.parentElement.offsetWidth;
        carouselContainer.style.transform = `translateX(-${currentIndex * containerWidth}px)`;
        updateDots();
    }

    // Auto-scroll function
    function autoScroll() {
        currentIndex = (currentIndex + 1) % totalItems;
        goToSlide(currentIndex);
    }

    // Initialize first dot as active
    updateDots();

    // Start auto-scrolling every 4 seconds
    setInterval(autoScroll, 4000);

    // Add click handlers to dots
    carouselDots.forEach((dot, index) => {
        dot.addEventListener('click', () => {
            goToSlide(index);
        });
    });

    // Add touch/swipe support for mobile
    let startX = 0;
    let isDragging = false;

    carouselContainer.addEventListener('touchstart', (e) => {
        startX = e.touches[0].clientX;
        isDragging = true;
    });

    carouselContainer.addEventListener('touchmove', (e) => {
        if (!isDragging) return;
        e.preventDefault();
    });

    carouselContainer.addEventListener('touchend', (e) => {
        if (!isDragging) return;

        const endX = e.changedTouches[0].clientX;
        const diffX = startX - endX;

        // Swipe threshold
        if (Math.abs(diffX) > 50) {
            if (diffX > 0) {
                // Swipe left - next item
                currentIndex = (currentIndex + 1) % totalItems;
            } else {
                // Swipe right - previous item
                currentIndex = (currentIndex - 1 + totalItems) % totalItems;
            }
            goToSlide(currentIndex);
        }

        isDragging = false;
    });

    // Handle window resize
    window.addEventListener('resize', () => {
        goToSlide(currentIndex);
    });
});
