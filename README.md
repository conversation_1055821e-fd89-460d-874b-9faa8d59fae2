# Picasso Mobile App

A hybrid Flutter mobile application that combines Flutter's native container with HTML/CSS/JavaScript for the user interface. This project uses a **WebView-based architecture** instead of traditional Flutter widgets and Dart screens.

## 🏗️ Architecture Overview

### **Hybrid Architecture**
Unlike typical Flutter projects that use Dart widgets and screen files, this project uses:
- **Flutter** as the native mobile container (Android/iOS)
- **HTML/CSS/JavaScript** for the entire user interface
- **WebView** as the bridge between Flutter and web technologies

```
┌─────────────────────────────────────┐
│           Flutter Container         │
│  ┌─────────────────────────────────┐ │
│  │         InAppWebView            │ │
│  │  ┌─────────────────────────────┐│ │
│  │  │     HTML/CSS/JavaScript     ││ │
│  │  │   (Complete UI & Logic)     ││ │
│  │  └─────────────────────────────┘│ │
│  └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

## 📁 Project Structure

### **Flutter Layer** (`lib/`)
```
lib/
└── main.dart          # Main Flutter app with WebView container
```

### **Web Assets Layer** (`assets/`)
```
assets/
├── progress.html                    # Main HTML file (UI structure)
├── progress-styles.css             # Main CSS file (styling)
├── main-app-logic.js              # Core app functionality
├── shop-navigation-module.js       # Navigation logic
├── camera-integration.js           # Camera functionality
├── mobile-camera-handler.js        # Mobile-specific camera handling
├── shop-crud-operations.js         # Shop operations
└── firebase-config.js             # Firebase configuration
```

### **Configuration Files**
```
pubspec.yaml           # Flutter dependencies
android/               # Android-specific configurations
ios/                   # iOS-specific configurations
web/                   # Web build configurations
```

## 🔧 How It Works

### **1. Flutter Container (`lib/main.dart`)**
The Flutter app serves as a native container that:
- Creates an `InAppWebView` widget
- Loads HTML content from assets
- Handles native permissions (camera, microphone, storage)
- Manages app lifecycle and system integration

```dart
class PicassoWebView extends StatefulWidget {
  // Creates WebView container
  // Loads HTML/CSS/JS assets
  // Handles native permissions
}
```

### **2. Asset Loading Process**
```dart
Future<void> _loadHtmlContent() async {
  // 1. Load main HTML file
  String mainHtml = await rootBundle.loadString('assets/progress.html');

  // 2. Load all JavaScript files
  String mainAppLogic = await rootBundle.loadString('assets/main-app-logic.js');
  String mobileCameraHandler = await rootBundle.loadString('assets/mobile-camera-handler.js');

  // 3. Inject JavaScript into HTML
  htmlContent = mainHtml
      .replaceAll('<script src="main-app-logic.js"></script>',
                 '<script>$mainAppLogic</script>');
}
```

### **3. WebView Configuration**
```dart
InAppWebView(
  initialData: InAppWebViewInitialData(data: htmlContent),
  initialSettings: InAppWebViewSettings(
    javaScriptEnabled: true,
    mediaPlaybackRequiresUserGesture: false,
    allowsInlineMediaPlayback: true,
    iframeAllow: "camera; microphone; geolocation",
    // Mobile-optimized settings
  ),
)
```

## 🎨 User Interface (HTML/CSS/JS)

### **Main HTML Structure** (`assets/progress.html`)
- **TikTok-style interface** with full-screen video cards
- **Mobile-first responsive design**
- **Touch-optimized interaction buttons**
- **Modal-based navigation system**

### **Key Features:**
- Vertical scrolling feed with snap-to-card behavior
- Authentication modals (login/signup)
- Shopping cart and checkout system
- Real-time messaging interface
- Camera integration for content creation
- Artist portfolio and marketplace

### **CSS Styling** (`assets/progress-styles.css`)
- **Dark theme** with gradient accents
- **Mobile-responsive** breakpoints
- **Touch-friendly** button sizes (minimum 44px)
- **Smooth animations** and transitions

### **JavaScript Functionality**
- **`main-app-logic.js`**: Core app functionality, navigation, user interactions
- **`mobile-camera-handler.js`**: Mobile-specific camera handling
- **`shop-navigation-module.js`**: E-commerce navigation and cart management
- **`camera-integration.js`**: Camera and media functionality
- **`shop-crud-operations.js`**: Shop operations and data management

## 📱 Mobile-Specific Features

### **Touch Interactions**
- Optimized for touch gestures
- Swipe navigation between content
- Touch-friendly button sizing
- Haptic feedback support

### **Camera Integration**
```javascript
function goToCameraTab() {
    // Mobile-specific camera handling
    document.body.style.overflow = "hidden";
    var camModal = document.getElementById('cameraModal');
    camModal.style.height = "100vh";
    camModal.style.width = "100vw";
}
```

### **Responsive Design**
- Viewport meta tag for proper mobile scaling
- Flexible layouts for different screen sizes
- Mobile-first CSS approach

## 🚀 Getting Started

### **Prerequisites**
- Flutter SDK (latest stable version)
- Android Studio / Xcode for mobile development
- Basic knowledge of HTML/CSS/JavaScript

### **Installation**
1. Clone the repository
2. Install Flutter dependencies:
   ```bash
   flutter pub get
   ```
3. Run the app:
   ```bash
   flutter run
   ```

### **Key Dependencies**
```yaml
dependencies:
  flutter_inappwebview: ^6.0.0    # WebView component
  permission_handler: ^11.3.1     # Native permissions
  path_provider: ^2.1.3           # File operations
```

## 🔄 Development Workflow

### **For UI Changes:**
1. Edit HTML structure in `assets/progress.html`
2. Modify styles in `assets/progress-styles.css`
3. Update functionality in respective JavaScript files
4. Hot reload will update the WebView content

### **For Native Features:**
1. Modify `lib/main.dart` for Flutter-specific functionality
2. Update permissions in `android/` or `ios/` folders
3. Rebuild the app for native changes

## ⚠️ Important Notes

### **Why This Architecture?**
- **Rapid Development**: Web technologies allow faster UI iteration
- **Cross-Platform**: Same HTML/CSS/JS works on Android and iOS
- **Rich UI**: Complex animations and interactions with CSS/JS
- **Team Expertise**: Leverage existing web development skills

### **Limitations**
- **Performance**: Slightly slower than native Flutter widgets
- **Platform Integration**: Limited access to some native features
- **Debugging**: Requires web debugging tools for UI issues
- **App Store**: Some restrictions on dynamic content loading

### **Best Practices**
- Keep JavaScript files modular and well-organized
- Use mobile-first responsive design principles
- Test on actual devices for touch interactions
- Optimize images and assets for mobile performance
- Handle network connectivity gracefully

## 🛠️ Troubleshooting

### **Common Issues**
1. **WebView not loading**: Check asset paths and file permissions
2. **JavaScript errors**: Use browser dev tools for debugging
3. **Camera not working**: Verify permissions in native code
4. **Styling issues**: Test on different screen sizes and orientations

### **Debugging**
- Use Chrome DevTools for HTML/CSS/JS debugging
- Flutter Inspector for WebView container issues
- Device logs for native permission problems

## 👥 Team Collaboration

### **For Frontend Developers**
- Focus on `assets/` folder for UI/UX changes
- Use standard web development tools and practices
- Test responsive design on various screen sizes

### **For Flutter Developers**
- Handle native integrations in `lib/main.dart`
- Manage app permissions and lifecycle
- Optimize WebView performance and settings

### **For Full-Stack Developers**
- Bridge between web assets and Flutter container
- Handle data flow between JavaScript and native code
- Manage build processes and deployment

## 📋 File Responsibilities

### **Critical Files to Understand**

| File | Purpose | Who Modifies |
|------|---------|--------------|
| `lib/main.dart` | Flutter container, WebView setup, native permissions | Flutter developers |
| `assets/progress.html` | Main UI structure, layout, modals | Frontend developers |
| `assets/progress-styles.css` | Styling, responsive design, animations | Frontend developers |
| `assets/main-app-logic.js` | Core app logic, navigation, interactions | Frontend/Full-stack developers |
| `assets/mobile-camera-handler.js` | Mobile camera functionality | Full-stack developers |
| `pubspec.yaml` | Flutter dependencies and asset declarations | Flutter developers |

### **Asset Management**
All web assets must be declared in `pubspec.yaml`:
```yaml
flutter:
  assets:
    - assets/progress.html
    - assets/progress-styles.css
    - assets/main-app-logic.js
    - assets/mobile-camera-handler.js
    # ... other assets
```

## 🔍 Code Examples

### **Adding a New Feature**

#### **1. Add HTML Structure**
```html
<!-- In assets/progress.html -->
<div id="newFeatureModal" class="fixed inset-0 bg-black/90 z-[100] hidden">
    <div class="bg-gray-900 p-6 rounded-xl">
        <h2>New Feature</h2>
        <button onclick="closeNewFeature()">Close</button>
    </div>
</div>
```

#### **2. Add CSS Styling**
```css
/* In assets/progress-styles.css */
#newFeatureModal {
    transition: opacity 0.3s ease;
}

#newFeatureModal.show {
    opacity: 1;
}
```

#### **3. Add JavaScript Functionality**
```javascript
// In assets/main-app-logic.js or separate file
function showNewFeature() {
    document.getElementById('newFeatureModal').classList.remove('hidden');
}

function closeNewFeature() {
    document.getElementById('newFeatureModal').classList.add('hidden');
}
```

### **Accessing Native Features**
```dart
// In lib/main.dart
Future<void> _requestPermissions() async {
  final permissions = [
    Permission.camera,
    Permission.microphone,
    Permission.storage,
  ];

  for (final permission in permissions) {
    await permission.request();
  }
}
```

## 🚦 Development Guidelines

### **Code Organization**
1. **Keep JavaScript modular**: Separate concerns into different files
2. **Use consistent naming**: Follow camelCase for JavaScript, kebab-case for CSS
3. **Comment complex logic**: Especially mobile-specific workarounds
4. **Test on devices**: Always test on actual mobile devices, not just simulators

### **Performance Considerations**
- **Optimize images**: Use appropriate formats and sizes for mobile
- **Minimize JavaScript**: Keep scripts lean for better WebView performance
- **Use CSS transforms**: For animations instead of changing layout properties
- **Lazy load content**: Load content as needed to improve initial load time

### **Mobile-First Development**
```css
/* Mobile-first approach */
.button {
    padding: 12px 16px; /* Mobile size */
    font-size: 16px;
}

/* Tablet and up */
@media (min-width: 768px) {
    .button {
        padding: 8px 12px;
        font-size: 14px;
    }
}
```

## 🧪 Testing Strategy

### **WebView Testing**
- Test HTML/CSS/JS in Chrome DevTools first
- Use Flutter's WebView debugging capabilities
- Test on multiple device sizes and orientations

### **Native Integration Testing**
- Test permissions on actual devices
- Verify camera and microphone access
- Test app lifecycle events (background/foreground)

### **Cross-Platform Testing**
- Test on both Android and iOS devices
- Verify consistent behavior across platforms
- Test different OS versions

## 📚 Additional Resources

### **Flutter WebView**
- [flutter_inappwebview documentation](https://inappwebview.dev/)
- [Flutter WebView best practices](https://docs.flutter.dev/development/platform-integration/web-views)

### **Mobile Web Development**
- [Mobile Web Development Guide](https://developers.google.com/web/fundamentals/design-and-ux/responsive)
- [Touch-friendly design principles](https://material.io/design/usability/accessibility.html)

### **Debugging Tools**
- Chrome DevTools for web content debugging
- Flutter Inspector for container debugging
- Platform-specific debugging tools (Android Studio, Xcode)

## 🤝 Contributing

When contributing to this project:

1. **For UI/UX changes**: Work primarily in the `assets/` folder
2. **For native features**: Modify `lib/main.dart` and platform folders
3. **Test thoroughly**: On both platforms and multiple devices
4. **Document changes**: Update this README for architectural changes
5. **Follow conventions**: Maintain consistent code style across web and Flutter code

## 📞 Support

For questions about this architecture:
- **Web-related issues**: Check browser console and network tab
- **Flutter-related issues**: Check Flutter logs and device logs
- **Integration issues**: Debug the bridge between WebView and Flutter

Remember: This is not a typical Flutter project - it's a hybrid approach that combines the best of both worlds! 🌟
