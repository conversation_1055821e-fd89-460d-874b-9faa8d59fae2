function goToCameraTab() {
    if (typeof startRecording === "function") {
        startRecording();
        // On mobile: prevent background scroll when modal is open
        document.body.style.overflow = "hidden";
        // Make camera modal full-screen on mobile
        var camModal = document.getElementById('cameraModal');
        if (camModal) {
            camModal.style.height = "100vh";
            camModal.style.width = "100vw";
            camModal.style.maxWidth = "100vw";
            camModal.style.maxHeight = "100vh";
            camModal.style.top = "0";
            camModal.style.left = "0";
        }
    }
}
// Restore scroll when camera modal is closed (assuming you use closeCameraModal)
const origCloseCameraModal = window.closeCameraModal;
window.closeCameraModal = function() {
    document.body.style.overflow = "";
    if (origCloseCameraModal) origCloseCameraModal();
};

// 1. TikTok-style nav and bottom nav
document.querySelectorAll('.nav-filter-btn, .nav-btn').forEach(btn => {
    const filter = btn.getAttribute('data-filter') || btn.getAttribute('data-tab');
    if (filter === 'busking' || filter === 'live') {
        btn.onclick = function(e) {
            e.preventDefault();
            goToCameraTab();
        };
    }
});
// 2. Fallback for any stray Busking/Live navs
document.querySelectorAll('button').forEach(btn => {
    if (
        btn.innerText.trim().toLowerCase().includes('busking') ||
        btn.innerText.trim().toLowerCase().includes('live')
    ) {
        btn.onclick = function(e) {
            e.preventDefault();
            goToCameraTab();
        };
    }
});