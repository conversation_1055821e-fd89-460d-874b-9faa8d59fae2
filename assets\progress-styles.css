
        :root {
  --bg-primary: #000;
  --bg-secondary: #111;
  --text-primary: #fff;
  --text-secondary: #aaa;
  --accent-primary: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
  --card-bg: #1a1a1a;
  --card-border: #333;
  --input-bg: #222;
}

[data-theme="light"] {
  --bg-primary: #fff;
  --bg-secondary: #f5f5f5;
  --text-primary: #000;
  --text-secondary: #555;
  --accent-primary: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
  --card-bg: #fff;
  --card-border: #ddd;
  --input-bg: #eee;
}

body {
  background: var(--bg-primary);
  color: var(--text-primary);
  font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON>', <PERSON><PERSON>, sans-serif;
  transition: all 0.3s ease;
}

.gradient-logo {
  background: var(--accent-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.action-btn {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

.brush-icon {
  background: var(--accent-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.live-badge {
  background: linear-gradient(45deg, #ff0050, #ff4081);
  animation: pulse 2s infinite;
}

.notification-badge {
  background: linear-gradient(45deg, #ff0050, #ff4081);
  position: absolute;
  top: -5px;
  right: -5px;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.category-pill {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 6px 12px;
  font-size: 0.8rem;
  margin: 5px;
  cursor: pointer;
  transition: all 0.2s;
}

.category-pill.active {
  background: var(--accent-primary);
  border: none;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.tip-btn {
  display: true; /* Hidden by default */
}

.busking-item .tip-btn {
  display: block; /* Show only for busking category */
}

.boost-btn {
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
}

.boost-btn:hover {
  background: linear-gradient(45deg, #764ba2 0%, #667eea 100%);
}

.buy-btn {
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
}

.buy-btn:hover {
  background: linear-gradient(45deg, #ee5a24, #ff6b6b);
  transform: translateY(-1px);
}

.follow-btn {
  background: linear-gradient(45deg, #667eea, #764ba2);
}

.follow-btn:hover {
  background: linear-gradient(45deg, #764ba2, #667eea);
}

.product-tag {
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.tab-active {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
}

.tab-inactive {
  background: rgba(255, 255, 255, 0.1);
  color: #ccc;
}

.top-nav-item {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.top-nav-item:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Camera Modal Styles */
.camera-modal {
  width: 100%;
  height: 100vh;
  max-width: none;
  max-height: none;
  margin: 0;
  border-radius: 0;
  background: #000;
  display: flex;
  flex-direction: column;
}

.camera-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
}

.close-camera, .camera-flip {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 10px;
  border-radius: 50%;
  transition: background 0.3s;
}

.close-camera:hover, .camera-flip:hover {
  background: rgba(255, 255, 255, 0.1);
}

.camera-container {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

#cameraPreview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.camera-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 30px;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.recording-timer {
  color: #ff4757;
  font-size: 18px;
  font-weight: bold;
  background: rgba(0, 0, 0, 0.8);
  padding: 8px 16px;
  border-radius: 20px;
}

.camera-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
}

.record-btn {
  width: 80px;
  height: 80px;
  border: 4px solid white;
  border-radius: 50%;
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
}

.record-btn:hover {
  transform: scale(1.1);
}

.record-btn.recording {
  border-color: #ff4757;
}

.record-circle {
  width: 60px;
  height: 60px;
  background: #ff4757;
  border-radius: 50%;
  transition: all 0.3s;
}

.record-btn.recording .record-circle {
  width: 30px;
  height: 30px;
  border-radius: 4px;
}

.camera-actions {
  display: flex;
  gap: 30px;
}

.camera-action {
  width: 50px;
  height: 50px;
  border: 2px solid rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 20px;
  cursor: pointer;
  transition: all 0.3s;
}

.camera-action:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: white;
}

/* Upload Area Styles */
.upload-area {
  border: 2px dashed #666;
  border-radius: 12px;
  padding: 40px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
  margin: 20px 0;
}

.upload-area:hover {
  border-color: #ff6b6b;
  background: rgba(255, 107, 107, 0.05);
}

.upload-area i {
  font-size: 48px;
  color: #666;
  margin-bottom: 16px;
}

.upload-area p {
  font-size: 18px;
  margin-bottom: 8px;
}

.upload-area small {
  color: #888;
}

.upload-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 20px;
}

/* Comments Section */
.comments-section {
  background: rgba(31, 41, 55, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(75, 85, 99, 0.3);
  /* Completely disable dragging and touch gestures */
  touch-action: none !important;
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  -webkit-touch-callout: none !important;
  -webkit-user-drag: none !important;
  -khtml-user-drag: none !important;
  -moz-user-drag: none !important;
  -o-user-drag: none !important;
  user-drag: none !important;
  pointer-events: auto;
  /* Prevent any transform animations from external sources */
  transition: transform 0.3s ease-in-out !important;
}

/* Override transform classes to ensure only our JavaScript can control them */
.comments-section.translate-x-full {
  transform: translateX(100%) !important;
}

.comments-section.translate-x-0 {
  transform: translateX(0) !important;
}

/* Prevent gestures on video cards that could trigger comments */
.relative:has(.comments-section) {
  /* Disable horizontal swipe gestures */
  touch-action: pan-y pinch-zoom !important;
  -webkit-touch-callout: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
}

/* Allow normal interaction on interactive elements */
.relative:has(.comments-section) button,
.relative:has(.comments-section) input,
.relative:has(.comments-section) textarea,
.relative:has(.comments-section) video,
.relative:has(.comments-section) .overflow-y-auto {
  touch-action: auto !important;
  user-select: auto !important;
  -webkit-user-select: auto !important;
  -moz-user-select: auto !important;
  -ms-user-select: auto !important;
}

/* Fallback for browsers that don't support :has() selector */
.video-card-container {
  touch-action: pan-y pinch-zoom !important;
  -webkit-touch-callout: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
}

/* Ensure categories flex container does not clip dropdown */
.px-4.mb-6 > .flex {
  overflow: visible !important;
  position: relative;
}

/* Services Dropdown */
.services-dropdown {
  background: rgba(31, 41, 55, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(75, 85, 99, 0.3);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
  z-index: 9999;
}

/* Dropdown Overlay Fixes */
#servicesDropdown,
#instrumentsDropdown {
    position: absolute !important;
    z-index: 99999 !important;
    pointer-events: auto !important;
}

/* Ensure parent containers don't clip dropdowns */
.relative {
    overflow: visible !important;
}

/* Force dropdowns to be on top */
.services-dropdown {
    position: absolute !important;
    z-index: 99999 !important;
    overflow: visible !important;
}

/* Mobile Optimizations */
@media (max-width: 768px) {
  .camera-modal {
      height: 100vh;
      height: 100dvh; /* Dynamic viewport height for mobile */
  }

  .header {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .header .flex.items-center.space-x-6 {
    gap: 0.5rem;
  }

  .header .flex.items-center.space-x-4 {
    gap: 0.5rem;
  }

  .main-nav {
    overflow-x: auto;
    scrollbar-width: none; /* Firefox */
  }

  .main-nav::-webkit-scrollbar {
    display: none; /* Safari and Chrome */
  }

  .main-nav .flex {
    padding-bottom: 0.5rem;
  }
  
  .messages-panel {
      width: 100vw !important;
      max-width: none !important;
      height: 100vh;
      height: 100dvh;
  }

  .search-overlay {
      padding: 10px;
  }

  .shopping-cart-panel {
      width: 100vw !important;
      max-width: none !important;
      height: 100vh;
      height: 100dvh;
      padding-top: 60px;
  }

  .favorites-panel {
      width: 100vw !important;
      max-width: none !important;
      height: 100vh;
      height: 100dvh;
      padding-top: 60px;
  }

  /* Bottom Slide Panel Styles */
  #bottomSlidePanel {
      backdrop-filter: blur(4px);
      -webkit-backdrop-filter: blur(4px);
  }

  #bottomSlideContent {
      max-height: 70vh;
      min-height: 300px;
      box-shadow: 0 -10px 25px rgba(0, 0, 0, 0.3);
  }

  /* Smooth transitions for mobile */
  @media (max-width: 768px) {
      #bottomSlideContent {
          transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }
  }

  /* iOS safe area support */
  @supports (padding-bottom: env(safe-area-inset-bottom)) {
      #bottomSlideContent {
          padding-bottom: calc(2rem + env(safe-area-inset-bottom));
      }
  }

  /* Android navigation bar support */
  @media screen and (max-height: 700px) {
      #bottomSlideContent {
          max-height: 60vh;
      }
  }

  /* TikTok-style top navigation styles */
  .shop-top-nav {
      background: transparent;
  }

  /* Cart badge animation */
  #cartCountBadge {
      animation: pulse 2s infinite;
  }

  @keyframes pulse {
      0% { transform: scale(1); }
      50% { transform: scale(1.1); }
      100% { transform: scale(1); }
  }

  /* Remove background and hover effects from top nav buttons */
  .shop-top-nav button {
      background: transparent !important;
      border: none;
      outline: none;
  }

  .shop-top-nav button:hover,
  .shop-top-nav button:active,
  .shop-top-nav button:focus {
      background: transparent !important;
      transform: none;
  }

  /* Subtle press effect for better UX */
  .shop-top-nav button:active {
      opacity: 0.7;
      transform: scale(0.95);
  }

  /* Enhanced scrollable categories */
  .categories-container {
      -webkit-overflow-scrolling: touch;
      scroll-behavior: smooth;
      overflow-x: auto;
      overflow-y: hidden;
  }

  .categories-container::-webkit-scrollbar {
      height: 0px;
      background: transparent;
  }

  .categories-container::-webkit-scrollbar-thumb {
      background: transparent;
  }

  /* Category button enhancements */
  .category-btn {
      flex-shrink: 0;
      user-select: none;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      touch-action: manipulation;
  }

  /* Ensure proper flex container for scrolling */
  .categories-container > div {
      display: flex;
      flex-wrap: nowrap;
  }

  /* Dropdown positioning */
  .category-dropdown {
      position: absolute;
      top: 100%;
      left: 0;
      z-index: 1000;
      margin-top: 8px;
  }

  .checkout-panel {
      width: 100vw !important;
      max-width: none !important;
      height: 100vh;
      height: 100dvh;
      padding-top: 60px;
  }

  /* Make modals full screen on mobile */
  .modal-content {
      width: 100vw;
      height: 100vh;
      max-width: none;
      max-height: none;
      border-radius: 0;
      margin: 0;
  }

  /* Adjust video interaction buttons for mobile */
  .video-interactions {
      right: 8px;
      bottom: 80px;
  }

  .video-interactions .action-btn {
      width: 44px;
      height: 44px;
      margin-bottom: 12px;
  }

  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 10px;
  }

  /* Better touch targets */
  .nav-btn {
      min-height: 48px;
      min-width: 48px;
  }

  /* Responsive text */
  .text-responsive {
      font-size: clamp(0.875rem, 2.5vw, 1rem);
  }
}

@media (max-width: 640px) {
  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 8px;
  }
}

/* Yellow state for available bid/tip buttons */
.bid-available {
  color: #fbbf24 !important;
}

.tip-available {
  color: #fbbf24 !important;
}



/* Top navigation icons only visible in home tab */
.home-only-nav {
  display: none;
}

.home-tab-active .home-only-nav {
  display: flex;
  align-items: center;
  transition: opacity 0.3s ease;
}

/* TikTok-style Navigation */
.tiktok-nav-btn {
  background: none;
  border: none;
  padding: 8px 0;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.tiktok-nav-btn:hover {
  transform: scale(1.05);
}

.tiktok-nav-btn span {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

/* Navigation underline */
.nav-underline {
  position: absolute;
  bottom: -4px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 2px;
  background: white;
  transition: width 0.3s ease;
  border-radius: 1px;
}

.tiktok-nav-btn.active .nav-underline {
  width: 60%;
}

/* TikTok-style active/inactive text colors */
.tiktok-nav-btn.active span {
  color: white !important;
}

.tiktok-nav-btn:not(.active) span {
  color: rgb(156, 163, 175) !important; /* text-gray-400 */
}

.tiktok-nav-btn.active i {
  color: white !important;
}

.tiktok-nav-btn:not(.active) i {
  color: rgb(156, 163, 175) !important; /* text-gray-400 */
}

/* Special positioning for Live button underline */
.live-underline {
  left: calc(50% + 4px); /* Offset to center under text, accounting for dot */
}

/* Simple red dot for Live button */
.live-dot {
  width: 6px;
  height: 6px;
  background: #ff0000;
  border-radius: 50%;
  margin-right: 6px;
  flex-shrink: 0; /* Prevent dot from shrinking */
}

/* Ensure Live button text aligns with other buttons */
.nav-filter-btn[data-filter="live"] .flex {
  align-items: center; /* Keep center alignment */
}

.nav-filter-btn[data-filter="live"] span {
  font-size: inherit; /* Ensure font size inheritance */
  line-height: inherit; /* Ensure line height inheritance */
}

/* Make sure all nav button text has consistent sizing */
.tiktok-nav-btn span {
  font-size: 0.875rem; /* 14px - text-sm */
  line-height: 1.25rem; /* 20px */
}

@media (min-width: 768px) {
  .tiktok-nav-btn span {
    font-size: 1rem; /* 16px - text-base */
    line-height: 1.5rem; /* 24px */
  }
}

/* Responsive adjustments for live dot */
@media (min-width: 768px) {
  .live-dot {
    width: 8px;
    height: 8px;
    margin-right: 8px;
  }
}

/* Ensure overlay doesn't interfere with scrolling */
.home-only-nav {
  pointer-events: none;
}

.home-only-nav > * {
  pointer-events: auto;
}


/* Hide scrollbar for Chrome, Safari and Opera */
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;     /* Firefox */
}


